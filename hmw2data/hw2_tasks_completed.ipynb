{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Homework 2: Preliminary EDA (Exploratory Data Analysis)\n", "\n", "**Course:** 4780/6780 Fundamentals of Data Science  \n", "**Instructor:** <PERSON><PERSON>  \n", "**Due:** 11pm, Monday, October 6, 2025\n", "\n", "---\n", "\n", "## How to use this notebook\n", "- Work top-to-bottom. Run each cell after you edit it.\n", "- Keep your answers clear and short.\n", "- Do not delete the problem statements.\n", "- You may add cells as needed.\n", "- When finished, run *Kernel → Restart & Run All*, verify outputs, then submit.\n", "\n", "\n", "**Honor code:** Write your own code. You may discuss high-level ideas with peers, but all implementation must be yours.\n", "\n", "---"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## The task\n", "Imagine you are a data scientist at a real estate company. Your task is to analyze Atlanta's real estate data. Start by inspecting the dataset *real_estate_ga.csv*, which contains property information from 2021. Familiarize yourself with its structure and contents before proceeding with EDA.\n", "\n", "**Requirements:**\n", "\n", "- Please use [pandas.DataFrame](http://pandas.pydata.org/pandas-docs/stable/generated/pandas.DataFrame.html) to manipulate data.\n", "\n", "- Please follow the python code style (https://www.python.org/dev/peps/pep-0008/). If TA finds your code hard to read, you will lose points. This requirement will stay for the whole semester."]}, {"cell_type": "code", "execution_count": 1, "metadata": {"execution": {"iopub.execute_input": "2025-10-06T23:57:22.027131Z", "iopub.status.busy": "2025-10-06T23:57:22.026877Z", "iopub.status.idle": "2025-10-06T23:57:23.442835Z", "shell.execute_reply": "2025-10-06T23:57:23.442489Z"}}, "outputs": [], "source": ["import numpy as np\n", "import pandas as pd"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 1. First, load the data as a DataFrame. \n", "*You don't need to do anything for this task as it has already been completed. Simply run cells.*"]}, {"cell_type": "code", "execution_count": 2, "metadata": {"execution": {"iopub.execute_input": "2025-10-06T23:57:23.444425Z", "iopub.status.busy": "2025-10-06T23:57:23.444309Z", "iopub.status.idle": "2025-10-06T23:57:23.548425Z", "shell.execute_reply": "2025-10-06T23:57:23.548123Z"}}, "outputs": [], "source": ["df = pd.read_csv(\"real_estate_ga.csv\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 2. Display the first 4 rows of the DataFrame `df`. [2pts]"]}, {"cell_type": "code", "execution_count": 3, "metadata": {"execution": {"iopub.execute_input": "2025-10-06T23:57:23.549784Z", "iopub.status.busy": "2025-10-06T23:57:23.549710Z", "iopub.status.idle": "2025-10-06T23:57:23.557236Z", "shell.execute_reply": "2025-10-06T23:57:23.557018Z"}}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Unnamed: 0</th>\n", "      <th>id</th>\n", "      <th>stateId</th>\n", "      <th>countyId</th>\n", "      <th>cityId</th>\n", "      <th>country</th>\n", "      <th>datePostedString</th>\n", "      <th>is_bankOwned</th>\n", "      <th>is_forAuction</th>\n", "      <th>event</th>\n", "      <th>...</th>\n", "      <th>parking</th>\n", "      <th>garageSpaces</th>\n", "      <th>hasGarage</th>\n", "      <th>levels</th>\n", "      <th>pool</th>\n", "      <th>spa</th>\n", "      <th>isNewConstruction</th>\n", "      <th>hasPetsAllowed</th>\n", "      <th>homeType</th>\n", "      <th>county</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>0</td>\n", "      <td>31503-*********</td>\n", "      <td>16</td>\n", "      <td>17</td>\n", "      <td>55064</td>\n", "      <td>USA</td>\n", "      <td>2021-07-12</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>Listed for sale</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>SINGLE_FAMILY</td>\n", "      <td>Brantley County</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>1</td>\n", "      <td>31503-********</td>\n", "      <td>16</td>\n", "      <td>18</td>\n", "      <td>55064</td>\n", "      <td>USA</td>\n", "      <td>2021-07-12</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>Listed for sale</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>SINGLE_FAMILY</td>\n", "      <td>Ware County</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2</td>\n", "      <td>31503-********</td>\n", "      <td>16</td>\n", "      <td>19</td>\n", "      <td>55064</td>\n", "      <td>USA</td>\n", "      <td>2021-07-10</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>Listed for sale</td>\n", "      <td>...</td>\n", "      <td>1</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>SINGLE_FAMILY</td>\n", "      <td>Ware County</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>3</td>\n", "      <td>31503-*********</td>\n", "      <td>16</td>\n", "      <td>20</td>\n", "      <td>55064</td>\n", "      <td>USA</td>\n", "      <td>2021-07-09</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>Listed for sale</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>SINGLE_FAMILY</td>\n", "      <td>Brantley County</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>4 rows × 39 columns</p>\n", "</div>"], "text/plain": ["   Unnamed: 0               id  stateId  countyId  cityId country  \\\n", "0           0  31503-*********       16        17   55064     USA   \n", "1           1   31503-********       16        18   55064     USA   \n", "2           2   31503-********       16        19   55064     USA   \n", "3           3  31503-*********       16        20   55064     USA   \n", "\n", "  datePostedString  is_bankOwned  is_forAuction            event  ...  \\\n", "0       2021-07-12             0              0  Listed for sale  ...   \n", "1       2021-07-12             0              0  Listed for sale  ...   \n", "2       2021-07-10             0              0  Listed for sale  ...   \n", "3       2021-07-09             0              0  Listed for sale  ...   \n", "\n", "   parking  garageSpaces  hasGarage levels pool  spa isNewConstruction  \\\n", "0        0           0.0          0      0    0    0                 0   \n", "1        0           0.0          0      0    0    0                 0   \n", "2        1           0.0          0      0    0    0                 0   \n", "3        0           0.0          0      0    0    0                 0   \n", "\n", "   hasPetsAllowed       homeType           county  \n", "0               0  SINGLE_FAMILY  Brantley County  \n", "1               0  SINGLE_FAMILY      Ware County  \n", "2               0  SINGLE_FAMILY      Ware County  \n", "3               0  SINGLE_FAMILY  Brantley County  \n", "\n", "[4 rows x 39 columns]"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["df.head(4)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 3. Display the 3 random rows of the DataFrame `df`. [2pts]"]}, {"cell_type": "code", "execution_count": 4, "metadata": {"execution": {"iopub.execute_input": "2025-10-06T23:57:23.576458Z", "iopub.status.busy": "2025-10-06T23:57:23.576351Z", "iopub.status.idle": "2025-10-06T23:57:23.581940Z", "shell.execute_reply": "2025-10-06T23:57:23.581682Z"}}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Unnamed: 0</th>\n", "      <th>id</th>\n", "      <th>stateId</th>\n", "      <th>countyId</th>\n", "      <th>cityId</th>\n", "      <th>country</th>\n", "      <th>datePostedString</th>\n", "      <th>is_bankOwned</th>\n", "      <th>is_forAuction</th>\n", "      <th>event</th>\n", "      <th>...</th>\n", "      <th>parking</th>\n", "      <th>garageSpaces</th>\n", "      <th>hasGarage</th>\n", "      <th>levels</th>\n", "      <th>pool</th>\n", "      <th>spa</th>\n", "      <th>isNewConstruction</th>\n", "      <th>hasPetsAllowed</th>\n", "      <th>homeType</th>\n", "      <th>county</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>6292</th>\n", "      <td>6292</td>\n", "      <td>30643-*********</td>\n", "      <td>16</td>\n", "      <td>219246</td>\n", "      <td>15479</td>\n", "      <td>USA</td>\n", "      <td>2021-07-13</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>Listed for sale</td>\n", "      <td>...</td>\n", "      <td>1</td>\n", "      <td>4.0</td>\n", "      <td>1</td>\n", "      <td>One and One Half</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>SINGLE_FAMILY</td>\n", "      <td>Hart County</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2736</th>\n", "      <td>2736</td>\n", "      <td>30038-********</td>\n", "      <td>16</td>\n", "      <td>103777</td>\n", "      <td>39452</td>\n", "      <td>USA</td>\n", "      <td>2021-06-25</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>Listed for sale</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>One</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>CONDO</td>\n", "      <td>Dekalb County</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8020</th>\n", "      <td>8020</td>\n", "      <td>30005-********</td>\n", "      <td>16</td>\n", "      <td>276567</td>\n", "      <td>16733</td>\n", "      <td>USA</td>\n", "      <td>2021-07-01</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>Listed for sale</td>\n", "      <td>...</td>\n", "      <td>1</td>\n", "      <td>2.0</td>\n", "      <td>1</td>\n", "      <td>Two</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>SINGLE_FAMILY</td>\n", "      <td>Fulton County</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>3 rows × 39 columns</p>\n", "</div>"], "text/plain": ["      Unnamed: 0               id  stateId  countyId  cityId country  \\\n", "6292        6292  30643-*********       16    219246   15479     USA   \n", "2736        2736   30038-********       16    103777   39452     USA   \n", "8020        8020   30005-********       16    276567   16733     USA   \n", "\n", "     datePostedString  is_bankOwned  is_forAuction            event  ...  \\\n", "6292       2021-07-13             0              0  Listed for sale  ...   \n", "2736       2021-06-25             0              0  Listed for sale  ...   \n", "8020       2021-07-01             0              0  Listed for sale  ...   \n", "\n", "      parking  garageSpaces  hasGarage            levels pool  spa  \\\n", "6292        1           4.0          1  One and One Half    0    0   \n", "2736        0           0.0          0               One    0    0   \n", "8020        1           2.0          1               Two    0    1   \n", "\n", "     isNewConstruction  hasPetsAllowed       homeType         county  \n", "6292                 0               0  SINGLE_FAMILY    Hart County  \n", "2736                 0               0          CONDO  Dekalb County  \n", "8020                 0               0  SINGLE_FAMILY  Fulton County  \n", "\n", "[3 rows x 39 columns]"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["df.sample(3)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 4. Display the 2 random rows of the DataFrame `df`. Make sure that the rows stay the same if one reruns this cell. [2pts]"]}, {"cell_type": "code", "execution_count": 5, "metadata": {"execution": {"iopub.execute_input": "2025-10-06T23:57:23.583045Z", "iopub.status.busy": "2025-10-06T23:57:23.582972Z", "iopub.status.idle": "2025-10-06T23:57:23.587864Z", "shell.execute_reply": "2025-10-06T23:57:23.587638Z"}}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Unnamed: 0</th>\n", "      <th>id</th>\n", "      <th>stateId</th>\n", "      <th>countyId</th>\n", "      <th>cityId</th>\n", "      <th>country</th>\n", "      <th>datePostedString</th>\n", "      <th>is_bankOwned</th>\n", "      <th>is_forAuction</th>\n", "      <th>event</th>\n", "      <th>...</th>\n", "      <th>parking</th>\n", "      <th>garageSpaces</th>\n", "      <th>hasGarage</th>\n", "      <th>levels</th>\n", "      <th>pool</th>\n", "      <th>spa</th>\n", "      <th>isNewConstruction</th>\n", "      <th>hasPetsAllowed</th>\n", "      <th>homeType</th>\n", "      <th>county</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>8412</th>\n", "      <td>8412</td>\n", "      <td>30228-********</td>\n", "      <td>16</td>\n", "      <td>282307</td>\n", "      <td>5051</td>\n", "      <td>USA</td>\n", "      <td>2021-07-08</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>Listed for sale</td>\n", "      <td>...</td>\n", "      <td>1</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>One</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>SINGLE_FAMILY</td>\n", "      <td>Henry County</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4092</th>\n", "      <td>4092</td>\n", "      <td>30005-********</td>\n", "      <td>16</td>\n", "      <td>147101</td>\n", "      <td>16733</td>\n", "      <td>USA</td>\n", "      <td>2021-07-06</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>Listed for sale</td>\n", "      <td>...</td>\n", "      <td>1</td>\n", "      <td>2.0</td>\n", "      <td>1</td>\n", "      <td>Two</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>SINGLE_FAMILY</td>\n", "      <td>Fulton County</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>2 rows × 39 columns</p>\n", "</div>"], "text/plain": ["      Unnamed: 0              id  stateId  countyId  cityId country  \\\n", "8412        8412  30228-********       16    282307    5051     USA   \n", "4092        4092  30005-********       16    147101   16733     USA   \n", "\n", "     datePostedString  is_bankOwned  is_forAuction            event  ...  \\\n", "8412       2021-07-08             0              0  Listed for sale  ...   \n", "4092       2021-07-06             0              0  Listed for sale  ...   \n", "\n", "      parking  garageSpaces  hasGarage levels pool  spa isNewConstruction  \\\n", "8412        1           0.0          0    One    0    0                 0   \n", "4092        1           2.0          1    Two    0    0                 0   \n", "\n", "      hasPetsAllowed       homeType         county  \n", "8412               0  SINGLE_FAMILY   Henry County  \n", "4092               0  SINGLE_FAMILY  Fulton County  \n", "\n", "[2 rows x 39 columns]"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["df.sample(2, random_state=42)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 5. How many row and columns are in the dataset? [2pts]"]}, {"cell_type": "code", "execution_count": 6, "metadata": {"execution": {"iopub.execute_input": "2025-10-06T23:57:23.589011Z", "iopub.status.busy": "2025-10-06T23:57:23.588940Z", "iopub.status.idle": "2025-10-06T23:57:23.590708Z", "shell.execute_reply": "2025-10-06T23:57:23.590464Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Number of rows: 13804\n", "Number of columns: 39\n"]}], "source": ["print(f\"Number of rows: {df.shape[0]}\")\n", "print(f\"Number of columns: {df.shape[1]}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 6. Print list of column names. [2pts]"]}, {"cell_type": "code", "execution_count": 7, "metadata": {"execution": {"iopub.execute_input": "2025-10-06T23:57:23.591859Z", "iopub.status.busy": "2025-10-06T23:57:23.591792Z", "iopub.status.idle": "2025-10-06T23:57:23.593382Z", "shell.execute_reply": "2025-10-06T23:57:23.593152Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["['Unnamed: 0', 'id', 'stateId', 'countyId', 'cityId', 'country', 'datePostedString', 'is_bankOwned', 'is_forAuction', 'event', 'time', 'price', 'pricePerSquareFoot', 'city', 'state', 'yearBuilt', 'streetAddress', 'zipcode', 'longitude', 'latitude', 'hasBadGeocode', 'description', 'currency', 'livingArea', 'livingAreaValue', 'lotAreaUnits', 'bathrooms', 'bedrooms', 'buildingArea', 'parking', 'garageSpaces', 'hasGarage', 'levels', 'pool', 'spa', 'isNewConstruction', 'hasPetsAllowed', 'homeType', 'county']\n"]}], "source": ["print(list(df.columns))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Ensure you understand the meaning of each column! This may require getting hands-on with the data - exploring it thoroughly and even opening the CSV file in Excel for a clearer view."]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 7. What is `Unnamed: 0` column? Can we delete it without losing essential information? Why? [3pts]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["The `Unnamed: 0` column appears to be an index column that was saved when the CSV file was created. It contains sequential numbers from 0 to the number of rows minus 1. This column doesn't provide any meaningful information about the properties themselves - it's just a row identifier. We can safely delete it without losing any essential information because pandas will automatically create its own index when we load the data."]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 8. Delete column `Unnamed: 0`. [2pts]"]}, {"cell_type": "code", "execution_count": 8, "metadata": {"execution": {"iopub.execute_input": "2025-10-06T23:57:23.594484Z", "iopub.status.busy": "2025-10-06T23:57:23.594414Z", "iopub.status.idle": "2025-10-06T23:57:23.597813Z", "shell.execute_reply": "2025-10-06T23:57:23.597553Z"}}, "outputs": [], "source": ["df = df.drop('Unnamed: 0', axis=1)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 9. Display a summary of the basic information for the numerical features in this DataFrame. [2pts]"]}, {"cell_type": "code", "execution_count": 9, "metadata": {"execution": {"iopub.execute_input": "2025-10-06T23:57:23.598832Z", "iopub.status.busy": "2025-10-06T23:57:23.598764Z", "iopub.status.idle": "2025-10-06T23:57:23.600269Z", "shell.execute_reply": "2025-10-06T23:57:23.600041Z"}}, "outputs": [], "source": ["pd.options.display.max_columns = None # You may want to this option to display all columns"]}, {"cell_type": "code", "execution_count": 10, "metadata": {"execution": {"iopub.execute_input": "2025-10-06T23:57:23.601279Z", "iopub.status.busy": "2025-10-06T23:57:23.601211Z", "iopub.status.idle": "2025-10-06T23:57:23.619846Z", "shell.execute_reply": "2025-10-06T23:57:23.619631Z"}}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>stateId</th>\n", "      <th>countyId</th>\n", "      <th>cityId</th>\n", "      <th>is_bankOwned</th>\n", "      <th>is_forAuction</th>\n", "      <th>time</th>\n", "      <th>price</th>\n", "      <th>pricePerSquareFoot</th>\n", "      <th>yearBuilt</th>\n", "      <th>zipcode</th>\n", "      <th>longitude</th>\n", "      <th>latitude</th>\n", "      <th>hasBadGeocode</th>\n", "      <th><PERSON><PERSON><PERSON></th>\n", "      <th>livingAreaValue</th>\n", "      <th>bathrooms</th>\n", "      <th>bedrooms</th>\n", "      <th>buildingArea</th>\n", "      <th>parking</th>\n", "      <th>garageSpaces</th>\n", "      <th>hasGarage</th>\n", "      <th>pool</th>\n", "      <th>spa</th>\n", "      <th>isNewConstruction</th>\n", "      <th>hasPetsAllowed</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>count</th>\n", "      <td>13804.0</td>\n", "      <td>13804.000000</td>\n", "      <td>13804.000000</td>\n", "      <td>13804.000000</td>\n", "      <td>13804.000000</td>\n", "      <td>1.378500e+04</td>\n", "      <td>1.380400e+04</td>\n", "      <td>13804.000000</td>\n", "      <td>13804.000000</td>\n", "      <td>13804.000000</td>\n", "      <td>13804.000000</td>\n", "      <td>13804.000000</td>\n", "      <td>13804.0</td>\n", "      <td>1.380400e+04</td>\n", "      <td>1.380400e+04</td>\n", "      <td>13804.000000</td>\n", "      <td>13804.000000</td>\n", "      <td>13804.000000</td>\n", "      <td>13804.000000</td>\n", "      <td>13804.000000</td>\n", "      <td>13804.000000</td>\n", "      <td>13804.000000</td>\n", "      <td>13804.000000</td>\n", "      <td>13804.000000</td>\n", "      <td>13804.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>mean</th>\n", "      <td>16.0</td>\n", "      <td>142659.266879</td>\n", "      <td>32140.423428</td>\n", "      <td>0.000145</td>\n", "      <td>0.000435</td>\n", "      <td>1.620448e+12</td>\n", "      <td>3.670973e+05</td>\n", "      <td>116.781730</td>\n", "      <td>1347.435961</td>\n", "      <td>30670.213851</td>\n", "      <td>-83.776647</td>\n", "      <td>33.345807</td>\n", "      <td>0.0</td>\n", "      <td>1.858872e+03</td>\n", "      <td>1.858872e+03</td>\n", "      <td>1.878079</td>\n", "      <td>2.348667</td>\n", "      <td>1176.989930</td>\n", "      <td>0.460301</td>\n", "      <td>0.488409</td>\n", "      <td>0.354825</td>\n", "      <td>0.054405</td>\n", "      <td>0.060707</td>\n", "      <td>0.045784</td>\n", "      <td>0.003839</td>\n", "    </tr>\n", "    <tr>\n", "      <th>std</th>\n", "      <td>0.0</td>\n", "      <td>98056.711946</td>\n", "      <td>37906.464824</td>\n", "      <td>0.012036</td>\n", "      <td>0.020845</td>\n", "      <td>3.373904e+10</td>\n", "      <td>6.478346e+05</td>\n", "      <td>1748.198773</td>\n", "      <td>928.113617</td>\n", "      <td>557.074716</td>\n", "      <td>1.050670</td>\n", "      <td>1.060855</td>\n", "      <td>0.0</td>\n", "      <td>4.307442e+04</td>\n", "      <td>4.307442e+04</td>\n", "      <td>1.886242</td>\n", "      <td>2.076541</td>\n", "      <td>1813.717292</td>\n", "      <td>0.498440</td>\n", "      <td>0.929491</td>\n", "      <td>0.478477</td>\n", "      <td>0.226822</td>\n", "      <td>0.238801</td>\n", "      <td>0.209024</td>\n", "      <td>0.061847</td>\n", "    </tr>\n", "    <tr>\n", "      <th>min</th>\n", "      <td>16.0</td>\n", "      <td>17.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>2.524608e+11</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>30002.000000</td>\n", "      <td>-85.583374</td>\n", "      <td>30.626259</td>\n", "      <td>0.0</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25%</th>\n", "      <td>16.0</td>\n", "      <td>58012.750000</td>\n", "      <td>13709.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>1.623283e+12</td>\n", "      <td>9.500000e+04</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>30206.000000</td>\n", "      <td>-84.456619</td>\n", "      <td>32.623352</td>\n", "      <td>0.0</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>50%</th>\n", "      <td>16.0</td>\n", "      <td>116674.500000</td>\n", "      <td>29242.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>1.625443e+12</td>\n", "      <td>2.470000e+05</td>\n", "      <td>95.000000</td>\n", "      <td>1965.000000</td>\n", "      <td>30530.000000</td>\n", "      <td>-84.071148</td>\n", "      <td>33.700008</td>\n", "      <td>0.0</td>\n", "      <td>1.280000e+03</td>\n", "      <td>1.280000e+03</td>\n", "      <td>2.000000</td>\n", "      <td>3.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>75%</th>\n", "      <td>16.0</td>\n", "      <td>227609.250000</td>\n", "      <td>43250.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>1.625875e+12</td>\n", "      <td>4.250000e+05</td>\n", "      <td>161.000000</td>\n", "      <td>1999.000000</td>\n", "      <td>31061.000000</td>\n", "      <td>-83.358637</td>\n", "      <td>34.038973</td>\n", "      <td>0.0</td>\n", "      <td>2.299250e+03</td>\n", "      <td>2.299250e+03</td>\n", "      <td>3.000000</td>\n", "      <td>4.000000</td>\n", "      <td>1996.250000</td>\n", "      <td>1.000000</td>\n", "      <td>0.000000</td>\n", "      <td>1.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>max</th>\n", "      <td>16.0</td>\n", "      <td>333689.000000</td>\n", "      <td>397383.000000</td>\n", "      <td>1.000000</td>\n", "      <td>1.000000</td>\n", "      <td>1.626221e+12</td>\n", "      <td>3.050400e+07</td>\n", "      <td>205000.000000</td>\n", "      <td>9999.000000</td>\n", "      <td>31909.000000</td>\n", "      <td>-80.841385</td>\n", "      <td>34.993996</td>\n", "      <td>0.0</td>\n", "      <td>5.057316e+06</td>\n", "      <td>5.057316e+06</td>\n", "      <td>89.000000</td>\n", "      <td>89.000000</td>\n", "      <td>87120.000000</td>\n", "      <td>1.000000</td>\n", "      <td>8.000000</td>\n", "      <td>1.000000</td>\n", "      <td>1.000000</td>\n", "      <td>1.000000</td>\n", "      <td>1.000000</td>\n", "      <td>1.000000</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["       stateId       countyId         cityId  is_bankOwned  is_forAuction  \\\n", "count  13804.0   13804.000000   13804.000000  13804.000000   13804.000000   \n", "mean      16.0  142659.266879   32140.423428      0.000145       0.000435   \n", "std        0.0   98056.711946   37906.464824      0.012036       0.020845   \n", "min       16.0      17.000000       0.000000      0.000000       0.000000   \n", "25%       16.0   58012.750000   13709.000000      0.000000       0.000000   \n", "50%       16.0  116674.500000   29242.000000      0.000000       0.000000   \n", "75%       16.0  227609.250000   43250.000000      0.000000       0.000000   \n", "max       16.0  333689.000000  397383.000000      1.000000       1.000000   \n", "\n", "               time         price  pricePerSquareFoot     yearBuilt  \\\n", "count  1.378500e+04  1.380400e+04        13804.000000  13804.000000   \n", "mean   1.620448e+12  3.670973e+05          116.781730   1347.435961   \n", "std    3.373904e+10  6.478346e+05         1748.198773    928.113617   \n", "min    2.524608e+11  0.000000e+00            0.000000      0.000000   \n", "25%    1.623283e+12  9.500000e+04            0.000000      0.000000   \n", "50%    1.625443e+12  2.470000e+05           95.000000   1965.000000   \n", "75%    1.625875e+12  4.250000e+05          161.000000   1999.000000   \n", "max    1.626221e+12  3.050400e+07       205000.000000   9999.000000   \n", "\n", "            zipcode     longitude      latitude  hasBadGeocode    livingArea  \\\n", "count  13804.000000  13804.000000  13804.000000        13804.0  1.380400e+04   \n", "mean   30670.213851    -83.776647     33.345807            0.0  1.858872e+03   \n", "std      557.074716      1.050670      1.060855            0.0  4.307442e+04   \n", "min    30002.000000    -85.583374     30.626259            0.0  0.000000e+00   \n", "25%    30206.000000    -84.456619     32.623352            0.0  0.000000e+00   \n", "50%    30530.000000    -84.071148     33.700008            0.0  1.280000e+03   \n", "75%    31061.000000    -83.358637     34.038973            0.0  2.299250e+03   \n", "max    31909.000000    -80.841385     34.993996            0.0  5.057316e+06   \n", "\n", "       livingAreaValue     bathrooms      bedrooms  buildingArea  \\\n", "count     1.380400e+04  13804.000000  13804.000000  13804.000000   \n", "mean      1.858872e+03      1.878079      2.348667   1176.989930   \n", "std       4.307442e+04      1.886242      2.076541   1813.717292   \n", "min       0.000000e+00      0.000000      0.000000      0.000000   \n", "25%       0.000000e+00      0.000000      0.000000      0.000000   \n", "50%       1.280000e+03      2.000000      3.000000      0.000000   \n", "75%       2.299250e+03      3.000000      4.000000   1996.250000   \n", "max       5.057316e+06     89.000000     89.000000  87120.000000   \n", "\n", "            parking  garageSpaces     hasGarage          pool           spa  \\\n", "count  13804.000000  13804.000000  13804.000000  13804.000000  13804.000000   \n", "mean       0.460301      0.488409      0.354825      0.054405      0.060707   \n", "std        0.498440      0.929491      0.478477      0.226822      0.238801   \n", "min        0.000000      0.000000      0.000000      0.000000      0.000000   \n", "25%        0.000000      0.000000      0.000000      0.000000      0.000000   \n", "50%        0.000000      0.000000      0.000000      0.000000      0.000000   \n", "75%        1.000000      0.000000      1.000000      0.000000      0.000000   \n", "max        1.000000      8.000000      1.000000      1.000000      1.000000   \n", "\n", "       isNewConstruction  hasPetsAllowed  \n", "count       13804.000000    13804.000000  \n", "mean            0.045784        0.003839  \n", "std             0.209024        0.061847  \n", "min             0.000000        0.000000  \n", "25%             0.000000        0.000000  \n", "50%             0.000000        0.000000  \n", "75%             0.000000        0.000000  \n", "max             1.000000        1.000000  "]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["df.describe()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Review the summary table above and ensure you understand the meaning of each row (e.g., standard deviation, 25th percentile). Then, carefully examine each column to grasp what is happening in the data."]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 10. Display a summary of the basic information for the categorical features in this DataFrame. [2pts]"]}, {"cell_type": "code", "execution_count": 11, "metadata": {"execution": {"iopub.execute_input": "2025-10-06T23:57:23.620889Z", "iopub.status.busy": "2025-10-06T23:57:23.620823Z", "iopub.status.idle": "2025-10-06T23:57:23.640828Z", "shell.execute_reply": "2025-10-06T23:57:23.640611Z"}}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>country</th>\n", "      <th>datePostedString</th>\n", "      <th>event</th>\n", "      <th>city</th>\n", "      <th>state</th>\n", "      <th>streetAddress</th>\n", "      <th>description</th>\n", "      <th>currency</th>\n", "      <th>lotAreaUnits</th>\n", "      <th>levels</th>\n", "      <th>homeType</th>\n", "      <th>county</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>count</th>\n", "      <td>13804</td>\n", "      <td>13804</td>\n", "      <td>13803</td>\n", "      <td>13785</td>\n", "      <td>13804</td>\n", "      <td>13804</td>\n", "      <td>13804</td>\n", "      <td>13705</td>\n", "      <td>13804</td>\n", "      <td>13804</td>\n", "      <td>13804</td>\n", "      <td>13804</td>\n", "      <td>13804</td>\n", "    </tr>\n", "    <tr>\n", "      <th>unique</th>\n", "      <td>12546</td>\n", "      <td>1</td>\n", "      <td>391</td>\n", "      <td>5</td>\n", "      <td>536</td>\n", "      <td>1</td>\n", "      <td>12452</td>\n", "      <td>12106</td>\n", "      <td>1</td>\n", "      <td>2</td>\n", "      <td>41</td>\n", "      <td>5</td>\n", "      <td>149</td>\n", "    </tr>\n", "    <tr>\n", "      <th>top</th>\n", "      <td>30161-76431604</td>\n", "      <td>USA</td>\n", "      <td>2021-06-25</td>\n", "      <td>Listed for sale</td>\n", "      <td>Atlanta</td>\n", "      <td>GA</td>\n", "      <td>Coming Soon Plan</td>\n", "      <td>Great Lot in Shadowmoor Subdivision to build y...</td>\n", "      <td>USD</td>\n", "      <td><PERSON><PERSON>s</td>\n", "      <td>0</td>\n", "      <td>SINGLE_FAMILY</td>\n", "      <td>Fulton County</td>\n", "    </tr>\n", "    <tr>\n", "      <th>freq</th>\n", "      <td>2</td>\n", "      <td>13804</td>\n", "      <td>2103</td>\n", "      <td>9520</td>\n", "      <td>1031</td>\n", "      <td>13804</td>\n", "      <td>30</td>\n", "      <td>13</td>\n", "      <td>13804</td>\n", "      <td>9797</td>\n", "      <td>6524</td>\n", "      <td>8111</td>\n", "      <td>1168</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                    id country datePostedString            event     city  \\\n", "count            13804   13804            13803            13785    13804   \n", "unique           12546       1              391                5      536   \n", "top     30161-76431604     USA       2021-06-25  Listed for sale  Atlanta   \n", "freq                 2   13804             2103             9520     1031   \n", "\n", "        state     streetAddress  \\\n", "count   13804             13804   \n", "unique      1             12452   \n", "top        GA  Coming Soon Plan   \n", "freq    13804                30   \n", "\n", "                                              description currency  \\\n", "count                                               13705    13804   \n", "unique                                              12106        1   \n", "top     Great Lot in Shadowmoor Subdivision to build y...      USD   \n", "freq                                                   13    13804   \n", "\n", "       lotAreaUnits levels       homeType         county  \n", "count         13804  13804          13804          13804  \n", "unique            2     41              5            149  \n", "top           Acres      0  SINGLE_FAMILY  Fulton County  \n", "freq           9797   6524           8111           1168  "]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["df.describe(include='object')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Review the summary table above and ensure you understand the meaning of each row (e.g., unique, top, freq). Then, carefully examine each column to grasp what is happening in the data."]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 11. Print number of unique values in each column of the DataFrame. [2pts]"]}, {"cell_type": "code", "execution_count": 12, "metadata": {"execution": {"iopub.execute_input": "2025-10-06T23:57:23.641875Z", "iopub.status.busy": "2025-10-06T23:57:23.641809Z", "iopub.status.idle": "2025-10-06T23:57:23.651974Z", "shell.execute_reply": "2025-10-06T23:57:23.651721Z"}}, "outputs": [{"data": {"text/plain": ["id                    12546\n", "stateId                   1\n", "countyId              13697\n", "cityId                  519\n", "country                   1\n", "datePostedString        391\n", "is_bankOwned              2\n", "is_forAuction             2\n", "event                     5\n", "time                    577\n", "price                  2459\n", "pricePerSquareFoot      544\n", "city                    536\n", "state                     1\n", "yearBuilt               168\n", "streetAddress         12452\n", "zipcode                 604\n", "longitude             12250\n", "latitude              12386\n", "hasBadGeocode             1\n", "description           12106\n", "currency                  1\n", "livingArea             3137\n", "livingAreaValue        3137\n", "lotAreaUnits              2\n", "bathrooms                17\n", "bedrooms                 18\n", "buildingArea           2786\n", "parking                   2\n", "garageSpaces              9\n", "hasGarage                 2\n", "levels                   41\n", "pool                      2\n", "spa                       2\n", "isNewConstruction         2\n", "hasPetsAllowed            2\n", "homeType                  5\n", "county                  149\n", "dtype: int64"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["df.nunique()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 12. Some columns contain only a single unique value. Which ones? Print their names. Then, delete all such columns and explain why removing them makes sense. [3pts]\n"]}, {"cell_type": "code", "execution_count": 13, "metadata": {"execution": {"iopub.execute_input": "2025-10-06T23:57:23.653081Z", "iopub.status.busy": "2025-10-06T23:57:23.653012Z", "iopub.status.idle": "2025-10-06T23:57:23.663302Z", "shell.execute_reply": "2025-10-06T23:57:23.663103Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Columns with only one unique value:\n", "['stateId', 'country', 'state', 'hasBadGeocode', 'currency']\n", "\n", "Dropped 5 columns\n"]}], "source": ["# Find columns with only one unique value\n", "single_value_cols = df.columns[df.nunique() == 1]\n", "print(\"Columns with only one unique value:\")\n", "print(list(single_value_cols))\n", "\n", "# Drop these columns\n", "df = df.drop(columns=single_value_cols)\n", "print(f\"\\nDropped {len(single_value_cols)} columns\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Removing columns with only one unique value makes sense because they provide no information for analysis or modeling. These columns have zero variance - they don't help distinguish between different properties or provide any insights. They just take up space and computational resources without adding value to our analysis."]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 13. Run the code cell below. Are the results the same or different? Whether they differ or not differ, explain why. [2pts]"]}, {"cell_type": "code", "execution_count": 14, "metadata": {"execution": {"iopub.execute_input": "2025-10-06T23:57:23.664416Z", "iopub.status.busy": "2025-10-06T23:57:23.664334Z", "iopub.status.idle": "2025-10-06T23:57:23.666299Z", "shell.execute_reply": "2025-10-06T23:57:23.666086Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["True\n", "True\n"]}], "source": ["print((df['livingArea'] == df['livingAreaValue']).all())\n", "print(df['livingArea'].equals(df['livingAreaValue']))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Both results are the same (True). The first method `(df['livingArea'] == df['livingAreaValue']).all()` checks if all corresponding values in the two columns are equal element by element, then returns True if all comparisons are True. The second method `df['livingArea'].equals(df['livingAreaValue'])` directly compares the entire Series objects for equality. Both return True because the columns contain identical values, indicating they are duplicate columns."]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 14. Display a summary of the numerical features again. Identify any columns with identical statistics. If such columns exist, verify whether they are true duplicates and keep only the first occurrence, removing all others. [3pts]"]}, {"cell_type": "code", "execution_count": 15, "metadata": {"execution": {"iopub.execute_input": "2025-10-06T23:57:23.667317Z", "iopub.status.busy": "2025-10-06T23:57:23.667249Z", "iopub.status.idle": "2025-10-06T23:57:23.685832Z", "shell.execute_reply": "2025-10-06T23:57:23.685598Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["            countyId         cityId  is_bankOwned  is_forAuction  \\\n", "count   13804.000000   13804.000000  13804.000000   13804.000000   \n", "mean   142659.266879   32140.423428      0.000145       0.000435   \n", "std     98056.711946   37906.464824      0.012036       0.020845   \n", "min        17.000000       0.000000      0.000000       0.000000   \n", "25%     58012.750000   13709.000000      0.000000       0.000000   \n", "50%    116674.500000   29242.000000      0.000000       0.000000   \n", "75%    227609.250000   43250.000000      0.000000       0.000000   \n", "max    333689.000000  397383.000000      1.000000       1.000000   \n", "\n", "               time         price  pricePerSquareFoot     yearBuilt  \\\n", "count  1.378500e+04  1.380400e+04        13804.000000  13804.000000   \n", "mean   1.620448e+12  3.670973e+05          116.781730   1347.435961   \n", "std    3.373904e+10  6.478346e+05         1748.198773    928.113617   \n", "min    2.524608e+11  0.000000e+00            0.000000      0.000000   \n", "25%    1.623283e+12  9.500000e+04            0.000000      0.000000   \n", "50%    1.625443e+12  2.470000e+05           95.000000   1965.000000   \n", "75%    1.625875e+12  4.250000e+05          161.000000   1999.000000   \n", "max    1.626221e+12  3.050400e+07       205000.000000   9999.000000   \n", "\n", "            zipcode     longitude      latitude    livingArea  \\\n", "count  13804.000000  13804.000000  13804.000000  1.380400e+04   \n", "mean   30670.213851    -83.776647     33.345807  1.858872e+03   \n", "std      557.074716      1.050670      1.060855  4.307442e+04   \n", "min    30002.000000    -85.583374     30.626259  0.000000e+00   \n", "25%    30206.000000    -84.456619     32.623352  0.000000e+00   \n", "50%    30530.000000    -84.071148     33.700008  1.280000e+03   \n", "75%    31061.000000    -83.358637     34.038973  2.299250e+03   \n", "max    31909.000000    -80.841385     34.993996  5.057316e+06   \n", "\n", "       livingAreaValue     bathrooms      bedrooms  buildingArea  \\\n", "count     1.380400e+04  13804.000000  13804.000000  13804.000000   \n", "mean      1.858872e+03      1.878079      2.348667   1176.989930   \n", "std       4.307442e+04      1.886242      2.076541   1813.717292   \n", "min       0.000000e+00      0.000000      0.000000      0.000000   \n", "25%       0.000000e+00      0.000000      0.000000      0.000000   \n", "50%       1.280000e+03      2.000000      3.000000      0.000000   \n", "75%       2.299250e+03      3.000000      4.000000   1996.250000   \n", "max       5.057316e+06     89.000000     89.000000  87120.000000   \n", "\n", "            parking  garageSpaces     hasGarage          pool           spa  \\\n", "count  13804.000000  13804.000000  13804.000000  13804.000000  13804.000000   \n", "mean       0.460301      0.488409      0.354825      0.054405      0.060707   \n", "std        0.498440      0.929491      0.478477      0.226822      0.238801   \n", "min        0.000000      0.000000      0.000000      0.000000      0.000000   \n", "25%        0.000000      0.000000      0.000000      0.000000      0.000000   \n", "50%        0.000000      0.000000      0.000000      0.000000      0.000000   \n", "75%        1.000000      0.000000      1.000000      0.000000      0.000000   \n", "max        1.000000      8.000000      1.000000      1.000000      1.000000   \n", "\n", "       isNewConstruction  hasPetsAllowed  \n", "count       13804.000000    13804.000000  \n", "mean            0.045784        0.003839  \n", "std             0.209024        0.061847  \n", "min             0.000000        0.000000  \n", "25%             0.000000        0.000000  \n", "50%             0.000000        0.000000  \n", "75%             0.000000        0.000000  \n", "max             1.000000        1.000000  \n", "Found duplicate columns: livingArea and livingAreaValue\n", "\n", "Removed duplicate columns: ['livingAreaValue']\n"]}], "source": ["# Display summary of numerical features\n", "numerical_summary = df.describe()\n", "print(numerical_summary)\n", "\n", "# Check for duplicate columns by comparing all values\n", "duplicate_cols = []\n", "cols = df.select_dtypes(include=[np.number]).columns.tolist()\n", "\n", "for i in range(len(cols)):\n", "    for j in range(i+1, len(cols)):\n", "        if df[cols[i]].equals(df[cols[j]]):\n", "            duplicate_cols.append(cols[j])\n", "            print(f\"Found duplicate columns: {cols[i]} and {cols[j]}\")\n", "\n", "# Remove duplicate columns\n", "if duplicate_cols:\n", "    df = df.drop(columns=duplicate_cols)\n", "    print(f\"\\nRemoved duplicate columns: {duplicate_cols}\")\n", "else:\n", "    print(\"\\nNo duplicate columns found\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 15. Identify all columns that contain exactly two unique values in the DataFrame and retrieve the unique values present in each of them.\n", "*You don't need to do anything for this task as it has already been completed. Simply run cells.*"]}, {"cell_type": "code", "execution_count": 16, "metadata": {"execution": {"iopub.execute_input": "2025-10-06T23:57:23.686843Z", "iopub.status.busy": "2025-10-06T23:57:23.686776Z", "iopub.status.idle": "2025-10-06T23:57:23.697625Z", "shell.execute_reply": "2025-10-06T23:57:23.697397Z"}}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>is_bankOwned</th>\n", "      <th>is_forAuction</th>\n", "      <th>lotAreaUnits</th>\n", "      <th>parking</th>\n", "      <th>hasGarage</th>\n", "      <th>pool</th>\n", "      <th>spa</th>\n", "      <th>isNewConstruction</th>\n", "      <th>hasPetsAllowed</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td><PERSON><PERSON>s</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>sqft</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   is_bankOwned  is_forAuction lotAreaUnits  parking  hasGarage  pool  spa  \\\n", "0             0              0        Acres        0          0     0    0   \n", "1             1              1         sqft        1          1     1    1   \n", "\n", "   isNewConstruction  hasPetsAllowed  \n", "0                  0               0  \n", "1                  1               1  "]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["df.loc[:, df.nunique() == 2].apply(pd.unique)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Learn how `.apply()` method works!"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 16. Something is wrong with `time`. [8pts]\n", "\n", "The column `time` contains strange numbers. Convert it into a readable datetime format. Keep in mind that the dataset contains property information from 2021.  \n", "\n", "Refer to the [Pandas documentation](https://pandas.pydata.org/pandas-docs/stable/reference/api/pandas.to_datetime.html) for `to_datetime()` for guidance. Play with the parameter `unit`."]}, {"cell_type": "code", "execution_count": 17, "metadata": {"execution": {"iopub.execute_input": "2025-10-06T23:57:23.698811Z", "iopub.status.busy": "2025-10-06T23:57:23.698740Z", "iopub.status.idle": "2025-10-06T23:57:23.701988Z", "shell.execute_reply": "2025-10-06T23:57:23.701743Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Sample time values:\n", "0    1.626048e+12\n", "1    1.626048e+12\n", "2    1.625875e+12\n", "3    1.625789e+12\n", "4    1.625530e+12\n", "Name: time, dtype: float64\n", "\n", "Time column data type: float64\n", "\n", "Converted time values:\n", "0   2021-07-12\n", "1   2021-07-12\n", "2   2021-07-10\n", "3   2021-07-09\n", "4   2021-07-06\n", "Name: time, dtype: datetime64[ns]\n"]}], "source": ["# First, let's examine the time column\n", "print(\"Sample time values:\")\n", "print(df['time'].head())\n", "print(f\"\\nTime column data type: {df['time'].dtype}\")\n", "\n", "# Convert time from milliseconds to datetime\n", "df['time'] = pd.to_datetime(df['time'], unit='ms')\n", "\n", "print(\"\\nConverted time values:\")\n", "print(df['time'].head())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 17. Is there another column contains the same time-related information. [2pts]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Yes, the `datePostedString` column contains the same time-related information as the `time` column, but in a human-readable string format (e.g., '2021-07-12'). The `time` column contains the same dates but in milliseconds since epoch format, which we converted to datetime objects."]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 18. Let's check for missing values in each column [4pts]"]}, {"cell_type": "code", "execution_count": 18, "metadata": {"execution": {"iopub.execute_input": "2025-10-06T23:57:23.703135Z", "iopub.status.busy": "2025-10-06T23:57:23.703049Z", "iopub.status.idle": "2025-10-06T23:57:23.712208Z", "shell.execute_reply": "2025-10-06T23:57:23.711981Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Missing values per column:\n", "description         99\n", "event               19\n", "time                19\n", "datePostedString     1\n", "dtype: int64\n", "\n", "Total missing values: 138\n", "Percentage of missing values: 0.03%\n"]}], "source": ["# Check for missing values in each column\n", "missing_values = df.isnull().sum()\n", "print(\"Missing values per column:\")\n", "print(missing_values[missing_values > 0].sort_values(ascending=False))\n", "\n", "print(f\"\\nTotal missing values: {df.isnull().sum().sum()}\")\n", "print(f\"Percentage of missing values: {(df.isnull().sum().sum() / (df.shape[0] * df.shape[1])) * 100:.2f}%\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 19. Can the missing values in the `time` column be inferred from other available data? If so, fill in the missing values appropriately. [8pts]"]}, {"cell_type": "code", "execution_count": 19, "metadata": {"execution": {"iopub.execute_input": "2025-10-06T23:57:23.713276Z", "iopub.status.busy": "2025-10-06T23:57:23.713207Z", "iopub.status.idle": "2025-10-06T23:57:23.716874Z", "shell.execute_reply": "2025-10-06T23:57:23.716643Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Missing values in time column before filling:\n", "19\n", "\n", "Rows with missing time: 19\n", "DatePostedString values where time is missing:\n", "498     2021-03-25\n", "727     2021-07-13\n", "744     2021-07-09\n", "752     2021-07-08\n", "3982    2021-06-07\n", "Name: datePostedString, dtype: object\n", "\n", "Missing values in time column after filling: 0\n"]}], "source": ["# Check if we can fill missing time values from datePostedString\n", "print(\"Missing values in time column before filling:\")\n", "print(df['time'].isnull().sum())\n", "\n", "# Check if datePostedString has values where time is missing\n", "missing_time_mask = df['time'].isnull()\n", "print(f\"\\nRows with missing time: {missing_time_mask.sum()}\")\n", "\n", "if missing_time_mask.sum() > 0:\n", "    # Check if datePostedString has values for these rows\n", "    print(\"DatePostedString values where time is missing:\")\n", "    print(df.loc[missing_time_mask, 'datePostedString'].head())\n", "    \n", "    # Fill missing time values from datePostedString\n", "    df.loc[missing_time_mask, 'time'] = pd.to_datetime(df.loc[missing_time_mask, 'datePostedString'])\n", "    \n", "    print(f\"\\nMissing values in time column after filling: {df['time'].isnull().sum()}\")\n", "else:\n", "    print(\"No missing values in time column to fill\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 20. Delete columns `datePostedString` and `description` [2pts]"]}, {"cell_type": "code", "execution_count": 20, "metadata": {"execution": {"iopub.execute_input": "2025-10-06T23:57:23.717873Z", "iopub.status.busy": "2025-10-06T23:57:23.717790Z", "iopub.status.idle": "2025-10-06T23:57:23.720822Z", "shell.execute_reply": "2025-10-06T23:57:23.720611Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Deleted columns: datePostedString and description\n", "DataFrame shape after deletion: (13804, 30)\n"]}], "source": ["# Delete the specified columns\n", "df = df.drop(columns=['datePostedString', 'description'])\n", "print(\"Deleted columns: datePostedString and description\")\n", "print(f\"DataFrame shape after deletion: {df.shape}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 21. Print rows where any value is missing, displaying only the columns: `event`, `time`, and `city`. [2pts]"]}, {"cell_type": "code", "execution_count": 21, "metadata": {"execution": {"iopub.execute_input": "2025-10-06T23:57:23.721863Z", "iopub.status.busy": "2025-10-06T23:57:23.721797Z", "iopub.status.idle": "2025-10-06T23:57:23.726223Z", "shell.execute_reply": "2025-10-06T23:57:23.726024Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Number of rows with missing values: 19\n", "\n", "Rows with missing values (showing event, time, city):\n", "     event       time           city\n", "498    NaN 2021-03-25        Atlanta\n", "727    NaN 2021-07-13       Savannah\n", "744    NaN 2021-07-09       Savannah\n", "752    NaN 2021-07-08       Savannah\n", "3982   NaN 2021-06-07       Juliette\n", "4317   NaN 2021-07-09         <PERSON><PERSON>\n", "5227   NaN 2021-07-09      Hiawassee\n", "6258   NaN 2021-07-02        Atlanta\n", "7237   NaN 2021-05-20    Thomasville\n", "7340   NaN 2021-06-15  Sandy Springs\n"]}], "source": ["# Find rows with any missing values and display specific columns\n", "rows_with_missing = df[df.isnull().any(axis=1)]\n", "print(f\"Number of rows with missing values: {len(rows_with_missing)}\")\n", "print(\"\\nRows with missing values (showing event, time, city):\")\n", "print(rows_with_missing[['event', 'time', 'city']].head(10))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 22. Change all values `NaN` in the `event` column to `Unknown` [3pts]"]}, {"cell_type": "code", "execution_count": 22, "metadata": {"execution": {"iopub.execute_input": "2025-10-06T23:57:23.727245Z", "iopub.status.busy": "2025-10-06T23:57:23.727183Z", "iopub.status.idle": "2025-10-06T23:57:23.730072Z", "shell.execute_reply": "2025-10-06T23:57:23.729851Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Missing values in event column before: 19\n", "Missing values in event column after: 0\n"]}], "source": ["# Replace NaN values in event column with 'Unknown'\n", "print(f\"Missing values in event column before: {df['event'].isnull().sum()}\")\n", "df['event'] = df['event'].fillna('Unknown')\n", "print(f\"Missing values in event column after: {df['event'].isnull().sum()}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 23. Do we still have missing values? [3pts]\n", "\n", "It appears that we no longer have any explicit missing values (`NaN`). However, missing data can sometimes be represented by zeros. Identify all columns where this might be the case."]}, {"cell_type": "code", "execution_count": 23, "metadata": {"execution": {"iopub.execute_input": "2025-10-06T23:57:23.731168Z", "iopub.status.busy": "2025-10-06T23:57:23.731085Z", "iopub.status.idle": "2025-10-06T23:57:23.736239Z", "shell.execute_reply": "2025-10-06T23:57:23.736021Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Current missing values:\n", "0\n", "\n", "Columns with zeros (potential missing values):\n", "yearBuilt: 4423 zeros\n", "bathrooms: 4697 zeros\n", "bedrooms: 4682 zeros\n", "livingArea: 5397 zeros\n", "pricePerSquareFoot: 5432 zeros\n"]}], "source": ["# Check for remaining missing values\n", "print(\"Current missing values:\")\n", "print(df.isnull().sum().sum())\n", "\n", "# Identify columns where zeros might represent missing values\n", "# These are typically columns where 0 doesn't make logical sense\n", "numeric_cols = df.select_dtypes(include=[np.number]).columns\n", "zero_counts = (df[numeric_cols] == 0).sum()\n", "\n", "print(\"\\nColumns with zeros (potential missing values):\")\n", "suspicious_cols = ['yearBuilt', 'bathrooms', 'bedrooms', 'livingArea', 'pricePerSquareFoot']\n", "for col in suspicious_cols:\n", "    if col in df.columns:\n", "        zero_count = (df[col] == 0).sum()\n", "        print(f\"{col}: {zero_count} zeros\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 24. In all those columns replace zeros with `NaN`s [2pts]"]}, {"cell_type": "code", "execution_count": 24, "metadata": {"execution": {"iopub.execute_input": "2025-10-06T23:57:23.737253Z", "iopub.status.busy": "2025-10-06T23:57:23.737189Z", "iopub.status.idle": "2025-10-06T23:57:23.742748Z", "shell.execute_reply": "2025-10-06T23:57:23.742422Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["yearBuilt: Replaced 4423 zeros with NaN\n", "bathrooms: Replaced 4697 zeros with NaN\n", "bedrooms: Replaced 4682 zeros with NaN\n", "livingArea: Replaced 5397 zeros with NaN\n", "pricePerSquareFoot: Replaced 5432 zeros with NaN\n", "\n", "Total missing values after replacement: 24631\n"]}], "source": ["# Replace zeros with NaN in columns where 0 doesn't make sense\n", "cols_to_replace = ['yearBuilt', 'bathrooms', 'bedrooms', 'livingArea', 'pricePerSquareFoot']\n", "\n", "for col in cols_to_replace:\n", "    if col in df.columns:\n", "        before_count = (df[col] == 0).sum()\n", "        df[col] = df[col].replace(0, np.nan)\n", "        after_count = df[col].isnull().sum()\n", "        print(f\"{col}: Replaced {before_count} zeros with NaN\")\n", "\n", "print(f\"\\nTotal missing values after replacement: {df.isnull().sum().sum()}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Can we reliably infer missing `cityId` values using `city`?\n", "\n", "To answer such question, we need to understand a few things."]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 25. Do all cities have unique `cityId`? Find cities wuth multiple IDs if any. [2pts]"]}, {"cell_type": "code", "execution_count": 25, "metadata": {"execution": {"iopub.execute_input": "2025-10-06T23:57:23.743744Z", "iopub.status.busy": "2025-10-06T23:57:23.743679Z", "iopub.status.idle": "2025-10-06T23:57:23.999191Z", "shell.execute_reply": "2025-10-06T23:57:23.998949Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Cities with multiple cityId values:\n", "city\n", "Atlanta       21\n", "Marietta       7\n", "Hampton        7\n", "Savannah       7\n", "Alpharetta     7\n", "              ..\n", "Hartwell       2\n", "Adrian         2\n", "Adel           2\n", "Cherry Log     2\n", "Hamilton       2\n", "Name: cityId, Length: 281, dtype: int64\n", "\n", "Atlanta:\n", "          city  cityId\n", "72     Atlanta   17797\n", "76     Atlanta       0\n", "84     Atlanta   42728\n", "86     Atlanta   49352\n", "117    Atlanta   37211\n", "184    Atlanta   41298\n", "607    Atlanta   23302\n", "616    Atlanta   17382\n", "1085   Atlanta   44978\n", "1086   Atlanta  130532\n", "1285   Atlanta   13709\n", "1462   Atlanta   27136\n", "1467   Atlanta  396606\n", "2207   Atlanta   24505\n", "3062   Atlanta   12562\n", "3858   Atlanta   37964\n", "3867   Atlanta   48085\n", "5989   Atlanta   11799\n", "6009   Atlanta  129049\n", "7602   Atlanta   44879\n", "11998  Atlanta  396602\n", "\n", "Marietta:\n", "          city  cityId\n", "1499  Marietta   12562\n", "4419  Marietta   27136\n", "5087  Marietta   32287\n", "5088  Marietta   53985\n", "7437  <PERSON>tta   23553\n", "8151  Marietta       0\n", "9112  Marietta   54219\n", "\n", "Hampton:\n", "         city  cityId\n", "1264  Hampton    5051\n", "1274  Hampton   54623\n", "4412  Hampton   29242\n", "8403  Hampton   32777\n", "8409  Hampton  127287\n", "8418  Hampton       0\n", "8420  Hampton   53015\n", "\n", "Savannah:\n", "           city  cityId\n", "727    Savannah   47584\n", "4121   Savannah       0\n", "5138   Savannah    4845\n", "8814   Savannah  131771\n", "11396  Savannah   33504\n", "12168  Savannah  130309\n", "12181  Savannah  129530\n", "\n", "Alpharetta:\n", "             city  cityId\n", "1791   Alpharetta   16733\n", "1796   Alpharetta  395772\n", "1810   Alpharetta   54219\n", "1981   Alpharetta  397383\n", "4080   Alpharetta       0\n", "7845   Alpharetta   44821\n", "12311  Alpharetta   36296\n", "\n", "Griffin:\n", "         city  cityId\n", "679   Griffin   11738\n", "683   <PERSON>   40170\n", "1265  Griffin    5051\n", "4413  Griffin  128378\n", "4512  <PERSON>   31479\n", "4534  Griffin       0\n", "\n", "Snellville:\n", "            city  cityId\n", "1997  Snellville   20535\n", "2004  Snellville    5573\n", "2010  Snellville   12386\n", "2018  Snellville       0\n", "5492  Snellville   39452\n", "\n", "Locust Grove:\n", "               city  cityId\n", "4424   Locust Grove   53015\n", "5676   Locust Grove       0\n", "5680   Locust Grove    8719\n", "10172  Locust Grove   32777\n", "12778  Locust Grove  127287\n", "\n", "Watkinsville:\n", "              city  cityId\n", "147   Watkinsville   55057\n", "8747  Watkinsville   23534\n", "9682  Watkinsville   51011\n", "9688  Watkinsville   21524\n", "9692  Watkinsville       0\n", "\n", "Sparta:\n", "        city  cityId\n", "2171  Sparta   27200\n", "2172  Sparta   48337\n", "2185  Sparta   22574\n", "2187  Sparta    5958\n", "2189  Sparta   11249\n", "\n", "Bishop:\n", "       city  cityId\n", "382  <PERSON>   51011\n", "384  <PERSON>  130539\n", "385  Bishop       0\n", "390  <PERSON>   11646\n", "394  Bishop   55057\n", "\n", "Rossville:\n", "           city  cityId\n", "8898  Rossville       0\n", "9184  Rossville  129794\n", "9186  Rossville  128578\n", "9188  Rossville   33748\n", "9205  Rossville   17427\n", "\n", "Ball Ground:\n", "              city  cityId\n", "3988   Ball Ground   23584\n", "3996   Ball Ground   44821\n", "3998   Ball Ground   37736\n", "4005   Ball Ground       0\n", "12784  Ball Ground   19558\n", "\n", "Dillard:\n", "          city  cityId\n", "6070   Dillard       0\n", "6072   <PERSON><PERSON><PERSON>   13867\n", "6073   <PERSON><PERSON><PERSON>   55543\n", "6082   <PERSON><PERSON><PERSON>   38202\n", "11492  <PERSON><PERSON><PERSON>    5515\n", "\n", "Conyers:\n", "         city  cityId\n", "976   <PERSON><PERSON>   51534\n", "995   Conyers   39452\n", "1921  Conyers   20665\n", "5067  Conyers   38047\n", "\n", "Stone Mountain:\n", "                 city  cityId\n", "2512   Stone Mountain   41084\n", "2519   Stone Mountain   41298\n", "4495   Stone Mountain   10840\n", "10143  Stone Mountain   12386\n", "\n", "<PERSON><PERSON>:\n", "        city  cityId\n", "691   <PERSON><PERSON>    5971\n", "3045  Milner   11738\n", "3051  Milner       0\n", "3055  Milner   32178\n", "\n", "Macon:\n", "        city  cityId\n", "661    Macon   32626\n", "787    Macon       0\n", "3115   Macon    9951\n", "12272  Macon    8707\n", "\n", "Acworth:\n", "         city  cityId\n", "6328  Acworth   32287\n", "6330  Acworth   16425\n", "6540  Acworth   48573\n", "7396  A<PERSON>worth   12562\n", "\n", "Palmetto:\n", "           city  cityId\n", "12701  Palmetto  127737\n", "12702  Palmetto   33309\n", "12707  Palmetto       0\n", "12713  Palmetto   53569\n", "\n", "Covington:\n", "           city  cityId\n", "1156  Covington   38047\n", "1159  Covington       0\n", "2457  Covington   22520\n", "5380  Covington   51534\n", "\n", "Buford:\n", "        city  cityId\n", "2347  Buford   54600\n", "2348  Buford       0\n", "2359  Buford   10614\n", "8377  <PERSON>uford   44836\n", "\n", "Midland:\n", "         city  cityId\n", "7980  Midland   17539\n", "7981  Midland   32847\n", "7984  Midland   38359\n", "7995  Midland   52086\n", "\n", "Blue Ridge:\n", "            city  cityId\n", "7188  Blue Ridge   36001\n", "8671  Blue Ridge   23748\n", "8694  Blue Ridge   17417\n", "9342  Blue Ridge   36962\n", "\n", "Crawfordville:\n", "               city  cityId\n", "1881  Crawfordville   20440\n", "1882  Crawfordville   51579\n", "1883  Crawfordville       0\n", "1884  Crawfordville    4985\n", "\n", "Valdosta:\n", "           city  cityId\n", "3886   Valdosta   54902\n", "9653   Valdosta    6726\n", "9760   Valdosta   17707\n", "13398  Valdosta    8925\n", "\n", "Cohutta:\n", "         city  cityId\n", "1783  Cohutta       0\n", "1787  Cohutta   27667\n", "3347  Cohutta   35274\n", "3352  Cohutta   38130\n", "\n", "Jefferson:\n", "            city  cityId\n", "10173  Jefferson  126880\n", "11259  <PERSON>   12114\n", "11260  Jefferson       0\n", "11283  Jefferson   49640\n", "\n", "Jonesboro:\n", "           city  cityId\n", "2984  Jonesboro    5051\n", "2985  Jonesboro   39176\n", "2994  Jonesboro  127287\n", "5304  Jonesboro       0\n", "\n", "Rome:\n", "       city  cityId\n", "2242   Rome   13576\n", "11341  Rome   23096\n", "11351  Rome   20758\n", "11357  Rome   48679\n", "\n", "Winston:\n", "        city  cityId\n", "453  <PERSON>   56883\n", "454  <PERSON>   11140\n", "461  <PERSON>   15901\n", "473  <PERSON>   43755\n", "\n", "Hull:\n", "       city  cityId\n", "2570   Hull    5234\n", "2578   Hull       0\n", "2579   Hull   23534\n", "12736  Hull   49224\n", "\n", "Jasper:\n", "         city  cityId\n", "10195  <PERSON>    9756\n", "12786  Jasper   32208\n", "12873  Jasper       0\n", "12880  Jasper   24325\n", "\n", "Carnesville:\n", "              city  cityId\n", "7306   Carnesville   10720\n", "10608  Carnesville       0\n", "10614  Carnesville   53180\n", "10619  Carnesville   29498\n", "\n", "Reidsville:\n", "            city  cityId\n", "3625  Reidsville   47331\n", "3627  Reidsville       0\n", "3633  Reidsville    5741\n", "3640  Reidsville   24884\n", "\n", "Bremen:\n", "         city  cityId\n", "762    Bremen   26025\n", "4414   Bremen    3796\n", "10736  Bremen   29455\n", "10738  Bremen       0\n", "\n", "Hogansville:\n", "            city  cityId\n", "267  Hogansville   55772\n", "268  Hogansville       0\n", "274  Hogansville   55925\n", "284  Hogansville   45530\n", "\n", "Decatur:\n", "          city  cityId\n", "101    Decatur   44879\n", "1099   Decatur       0\n", "3761   Decatur  130532\n", "12556  Decatur   44978\n", "\n", "Aragon:\n", "        city  cityId\n", "2829  Aragon    8981\n", "7110  Aragon   23505\n", "7112  Aragon   20758\n", "7113  Aragon   40909\n", "\n", "Good Hope:\n", "           city  cityId\n", "4359  Good Hope       0\n", "4361  Good Hope   51011\n", "7860  <PERSON> Hope   11646\n", "7864  <PERSON> Hope   12798\n", "\n", "Powder Springs:\n", "                 city  cityId\n", "6985   Powder Springs   53985\n", "6988   Powder Springs   11140\n", "6995   Powder Springs   24307\n", "10023  Powder Springs   23553\n", "\n", "Douglasville:\n", "              city  cityId\n", "1529  Douglasville   11140\n", "5396  Douglasville   56883\n", "7356  Douglasville   43755\n", "7363  Douglasville   18586\n", "\n", "Claxton:\n", "         city  cityId\n", "1169  Claxton       0\n", "1170  Claxton   30920\n", "1175  Claxton   13244\n", "1176  Claxton   29989\n", "\n", "Clermont:\n", "          city  cityId\n", "3667  Clermont   44637\n", "3672  Clermont   28306\n", "3674  Clermont   24822\n", "3685  Clermont   22263\n", "\n", "Danville:\n", "          city  cityId\n", "3971  Danville    3360\n", "3972  Danville   23206\n", "3973  Danville       0\n", "\n", "Dallas:\n", "        city  cityId\n", "3464  Dallas   24307\n", "3483  Dallas   18586\n", "7208  Dallas   30775\n", "\n", "Newborn:\n", "         city  cityId\n", "8078  <PERSON><PERSON>   53560\n", "8087  <PERSON><PERSON>   46363\n", "8089  <PERSON><PERSON>   50579\n", "\n", "Danielsville:\n", "              city  cityId\n", "3116  Danielsville       0\n", "3117  Danielsville   44855\n", "3126  Danielsville   49224\n", "\n", "Dacula:\n", "        city  cityId\n", "3546  Dacula   44836\n", "3560  Dacula    5573\n", "3567  Dacula   11982\n", "\n", "<PERSON>:\n", "         city  cityId\n", "6358  <PERSON>   28373\n", "6359  <PERSON>    5741\n", "6360  Collins   47331\n", "\n", "Norcross:\n", "          city  cityId\n", "222   Norcross   53588\n", "226   Norcross   42728\n", "1202  Norcross   12386\n", "\n", "Ochlocknee:\n", "            city  cityId\n", "7684  Ochlocknee   51223\n", "7687  Ochlocknee   34268\n", "7688  Ochlocknee   46858\n", "\n", "Concord:\n", "        city  cityId\n", "840  Concord   21747\n", "842  Concord   14654\n", "843  Concord   23311\n", "\n", "Pembroke:\n", "          city  cityId\n", "9707  Pembroke   13244\n", "9709  Pembroke   55790\n", "9711  Pembroke   51159\n", "\n", "Columbus:\n"]}, {"name": "stdout", "output_type": "stream", "text": ["          city  cityId\n", "2751  Columbus   17539\n", "4787  Columbus       0\n", "4851  Columbus   32847\n", "\n", "<PERSON>:\n", "       city  cityId\n", "3509  Perry   13257\n", "5696  <PERSON>   25355\n", "9882  <PERSON>   11298\n", "\n", "<PERSON>:\n", "         city  cityId\n", "3127  Colbert       0\n", "4411  Colbert   48906\n", "7879  <PERSON>   29915\n", "\n", "Hephzibah:\n", "           city  cityId\n", "8501  Hephzibah   10210\n", "8504  Hephzibah   25096\n", "8510  Hephzibah       0\n", "\n", "Resaca:\n", "         city  cityId\n", "13668  Resaca       0\n", "13672  Resaca   15980\n", "13674  Resaca   23912\n", "\n", "Clayton:\n", "          city  cityId\n", "7655   Clayton   55543\n", "7658   Clayton       0\n", "11490  Clayton   14139\n", "\n", "Nahunta:\n", "          city  cityId\n", "12215  Nahunta   35867\n", "12217  Nahunta   34815\n", "12221  Nahunta   22820\n", "\n", "Zebulon:\n", "         city  cityId\n", "4751  Zebulon   11738\n", "4752  Zebulon   29336\n", "4763  Zebulon   39731\n", "\n", "Mountain City:\n", "                city  cityId\n", "2422   Mountain City       0\n", "12017  Mountain City   46648\n", "12027  Mountain City   55543\n", "\n", "Loganville:\n", "             city  cityId\n", "4426   Loganville    5573\n", "5519   Loganville   53026\n", "11010  Loganville  132519\n", "\n", "Hawkinsville:\n", "              city  cityId\n", "3012  Hawkinsville   56935\n", "3013  Hawkins<PERSON>   11298\n", "3022  Hawkinsville   24144\n", "\n", "Hinesville:\n", "            city  cityId\n", "3693  Hinesville   32048\n", "3700  Hinesville   24719\n", "5509  Hinesville   10083\n", "\n", "Hiram:\n", "       city  cityId\n", "3284  <PERSON><PERSON>   18586\n", "3292  <PERSON><PERSON>   24307\n", "3295  <PERSON>ram   53985\n", "\n", "<PERSON>:\n", "       city  cityId\n", "4657  <PERSON>    5198\n", "4659  <PERSON>   10720\n", "4662  <PERSON>   37982\n", "\n", "Hoschton:\n", "           city  cityId\n", "10552  <PERSON><PERSON><PERSON>   11982\n", "10557  Hoschton       0\n", "10558  Hoschton   35130\n", "\n", "Greensboro:\n", "            city  cityId\n", "1430  Greensboro    4985\n", "1571  Greensboro   22574\n", "2591  Greensboro   18752\n", "\n", "Gray:\n", "      city  cityId\n", "3393  Gray   45539\n", "3406  Gray       0\n", "3413  <PERSON>   16275\n", "\n", "<PERSON>:\n", "          city  cityId\n", "5688  <PERSON>   13257\n", "5689  <PERSON>   25355\n", "5700  Kathleen   30522\n", "\n", "Lake Park:\n", "           city  cityId\n", "3941  Lake Park       0\n", "3945  Lake Park   21919\n", "3970  Lake Park   54902\n", "\n", "Gainesville:\n", "              city  cityId\n", "1226   Gainesville   24822\n", "1254   Gainesville    8713\n", "10102  Gainesville   44821\n", "\n", "<PERSON>:\n", "           city  cityId\n", "5466   Franklin       0\n", "10279  <PERSON>  127706\n", "10292  <PERSON>   18188\n", "\n", "<PERSON><PERSON>:\n", "         city  cityId\n", "4675  <PERSON><PERSON>   23096\n", "4676  <PERSON><PERSON>   40909\n", "4681  <PERSON><PERSON>   17351\n", "\n", "<PERSON><PERSON>:\n", "          city  cityId\n", "3089   Liz<PERSON>    9951\n", "3095   <PERSON><PERSON>   32626\n", "13502  <PERSON><PERSON>   18858\n", "\n", "Folkston:\n", "          city  cityId\n", "4904  Folkston       0\n", "4905  Folkston   15428\n", "4927  Folkston   34815\n", "\n", "Ludowici:\n", "          city  cityId\n", "3174  <PERSON><PERSON><PERSON><PERSON>   15510\n", "3177  <PERSON><PERSON><PERSON><PERSON>   10083\n", "3178  <PERSON><PERSON>wi<PERSON>   32048\n", "\n", "<PERSON>:\n", "        city  cityId\n", "7904  Morrow   25992\n", "7908  <PERSON>   31592\n", "7910  Morrow       0\n", "\n", "Lula:\n", "      city  cityId\n", "3734  Lula   46340\n", "3747  Lula   10103\n", "3753  Lula   24822\n", "\n", "Mableton:\n", "           city  cityId\n", "6924   Mableton   32619\n", "6932   <PERSON><PERSON>ton   27136\n", "10016  Mableton   23553\n", "\n", "<PERSON>:\n", "        city  cityId\n", "2779  <PERSON>   53180\n", "2782  <PERSON>   18964\n", "2796  Martin       0\n", "\n", "Maysville:\n", "           city  cityId\n", "7626  Maysville   18281\n", "9225  Maysville       0\n", "9232  <PERSON><PERSON>   16605\n", "\n", "Ellenwood:\n", "           city  cityId\n", "105   <PERSON>wood   51882\n", "8036  Ellenwood       0\n", "8056  <PERSON><PERSON>   31011\n", "\n", "Ellaville:\n", "           city  cityId\n", "6449  Ellaville   50720\n", "6450  Ellaville    8494\n", "6454  Ellaville   51181\n", "\n", "Mc Caysville:\n", "              city  cityId\n", "7179  Mc Caysville   36001\n", "7180  Mc Caysville   23748\n", "7193  Mc Caysville   24601\n", "\n", "Reynolds:\n", "          city  cityId\n", "4746  Reynolds    6739\n", "4747  Reynolds    5258\n", "4749  <PERSON>   23892\n", "\n", "Dublin:\n", "         city  cityId\n", "4440   Dublin   24437\n", "9561   Dublin       0\n", "12042  Dublin    4467\n", "\n", "<PERSON><PERSON>:\n", "        city  cityId\n", "1421  <PERSON><PERSON>   23311\n", "1422  <PERSON><PERSON>   54685\n", "1423  <PERSON><PERSON>   29336\n", "\n", "Doraville:\n", "          city  cityId\n", "74   Doraville   42728\n", "80   Doraville   17797\n", "172  Doraville       0\n", "\n", "Monroe:\n", "         city  cityId\n", "7508   Monroe   12798\n", "7509   Monroe       0\n", "10501  Monroe  127174\n", "\n", "Morganton:\n", "           city  cityId\n", "7447  Morganton   39863\n", "7461  Morganton   51023\n", "7472  Morganton    7298\n", "\n", "Demorest:\n", "          city  cityId\n", "4141  Demorest   24221\n", "4144  <PERSON>more<PERSON>   51689\n", "4146  <PERSON><PERSON><PERSON>    8235\n", "\n", "Rex:\n", "     city  cityId\n", "6392  Rex   26739\n", "6394  Rex       0\n", "6401  Rex   20665\n", "\n", "Hazlehurst:\n", "            city  cityId\n", "1711  Hazlehurst       0\n", "1713  <PERSON>zle<PERSON>   11661\n", "1714  <PERSON>zle<PERSON>   55665\n", "\n", "Buckhead:\n", "          city  cityId\n", "6866  <PERSON><PERSON>    3871\n", "6867  <PERSON><PERSON>   11249\n", "6884  Buckhead   46363\n", "\n", "White:\n", "       city  cityId\n", "1854  White       0\n", "1865  White   41688\n", "1868  White   30775\n", "\n", "Scottdale:\n", "           city  cityId\n", "2141  Scottdale   43068\n", "2143  Scottdale   10840\n", "2152  Scottdale       0\n", "\n", "Waynesboro:\n", "            city  cityId\n", "2318  Waynesboro   55067\n", "2339  Waynesboro   36622\n", "7792  Waynesboro   46029\n", "\n", "Waverly Hall:\n", "              city  cityId\n", "2027  Waverly Hall   38359\n", "2029  Waverly Hall   22818\n", "2031  Waverly Hall   27084\n", "\n", "<PERSON>:\n", "          city  cityId\n", "4341  <PERSON>    8278\n", "4350  Buchanan    3796\n", "4357  Buchanan       0\n", "\n", "Smyrna:\n", "        city  cityId\n", "500   Smyrna   27136\n", "511   Smyrna   23553\n", "9139  Smyrna  396606\n", "\n", "Soperton:\n", "          city  cityId\n", "8187  Soperton       0\n", "8188  Soperton   54459\n", "8192  Soperton    6808\n", "\n", "Auburn:\n", "        city  cityId\n", "1304  Auburn   35130\n", "1308  Auburn   44836\n", "1318  Auburn   11982\n", "\n", "Springfield:\n", "              city  cityId\n", "6896   Springfield   42941\n", "6898   Springfield   31894\n", "13202  Springfield   10880\n", "\n", "Bonaire:\n", "        city  cityId\n", "197  Bonaire   30522\n", "207  Bonaire   25355\n", "219  Bonaire   27768\n", "\n", "Sylvania:\n", "          city  cityId\n", "4211  Sylvania   34215\n", "4212  Sylvania       0\n", "4238  Sylvania   52532\n", "\n", "Bogart:\n", "         city  cityId\n", "11782  <PERSON><PERSON>   21524\n", "11789  Bogart   20640\n", "11795  Bogart   55057\n", "\n", "Blythe:\n", "        city  cityId\n", "9001  Blythe   10210\n", "9003  <PERSON><PERSON>the   10466\n", "9004  Blythe       0\n", "\n", "Union Pt:\n", "          city  cityId\n", "3458  Union Pt   29603\n", "3459  Union Pt       0\n", "3461  Union Pt   39674\n", "\n", "Barnesville:\n", "             city  cityId\n", "8610  Barnesville   48830\n", "8611  Barnesville       0\n", "8613  Barnesville   14735\n", "\n", "Tallapoosa:\n", "             city  cityId\n", "11047  Tallapoosa   29808\n", "11049  Tallapoosa    8278\n", "11052  Tallapoosa       0\n", "\n", "Butler:\n", "         city  cityId\n", "12891  <PERSON>   18782\n", "12892  <PERSON>   23892\n", "12899  <PERSON>    8962\n", "\n", "<PERSON>:\n", "        city  cityId\n", "1127  <PERSON>   41298\n", "2904  <PERSON>   53588\n", "2907  Tucker       0\n", "\n", "Alamo:\n", "        city  cityId\n", "12211  Alamo   21811\n", "12212  Alamo   23136\n", "12213  Alamo   24888\n", "\n", "Rock Spring:\n", "             city  cityId\n", "3494  Rock Spring   13555\n", "3495  Rock Spring   36029\n", "9924  Rock Spring   52838\n", "\n", "Royston:\n", "         city  cityId\n", "7299  <PERSON><PERSON>   18190\n", "7300  Royston       0\n", "7303  <PERSON><PERSON>   40658\n", "\n", "Cave Spring:\n", "              city  cityId\n", "13449  Cave Spring   48679\n", "13453  Cave Spring   17351\n", "13459  Cave Spring   13576\n", "\n", "Chamblee:\n", "          city  cityId\n", "617   <PERSON><PERSON><PERSON>   17382\n", "618   <PERSON><PERSON><PERSON>   23302\n", "1141  Chamblee       0\n", "\n", "Canon:\n", "       city  cityId\n", "7304  Canon       0\n", "8536  Canon   17286\n", "8540  Canon   17131\n", "\n", "Cartersville:\n", "              city  cityId\n", "5953  Cartersville   30775\n", "5969  Cartersville   16425\n", "7116  Cartersville       0\n", "\n", "Canton:\n", "        city  cityId\n", "3144  Canton   37736\n", "3145  Canton   32058\n", "7484  Canton   48573\n", "\n", "Chatsworth:\n", "             city  cityId\n", "1780   Chatsworth   37836\n", "4437   Chatsworth   17618\n", "13759  Chatsworth       0\n", "\n", "Carrollton:\n", "            city  cityId\n", "1650  Carrollton   17317\n", "1673  <PERSON><PERSON>   26025\n", "6376  <PERSON><PERSON>   15901\n", "\n", "Adairsville:\n", "              city  cityId\n", "10388  Adairsville    3312\n", "10398  Adairsville       0\n", "10406  Adairsville   23912\n", "\n", "Lithonia:\n", "          city  cityId\n", "2709  Lithonia   39452\n", "3430  Lithonia   41084\n", "\n", "Uvalda:\n", "        city  cityId\n", "1342  Uvalda       0\n", "1347  Uvalda    7652\n", "\n", "Woodstock:\n", "           city  cityId\n", "4587  Woodstock   48573\n", "4613  Woodstock   37736\n", "\n", "Upatoi:\n", "       city  cityId\n", "246  Upatoi       0\n", "251  Upatoi   22818\n", "\n", "Wrens:\n", "       city  cityId\n", "3659  Wrens   41791\n", "3665  Wrens   29273\n", "\n", "Wrightsville:\n", "              city  cityId\n", "8554  Wrightsville   48587\n", "8560  Wrightsville   20772\n", "\n", "Union Point:\n", "             city  cityId\n", "3457  Union Point       0\n", "3462  Union Point   29603\n", "\n", "Union City:\n", "            city  cityId\n", "3864  Union City       0\n", "9565  Union City   48085\n", "\n", "Waleska:\n"]}, {"name": "stdout", "output_type": "stream", "text": ["         city  cityId\n", "3827  Waleska   27739\n", "3854  Waleska   37736\n", "\n", "<PERSON><PERSON><PERSON>:\n", "          city  cityId\n", "10782  Lumpkin       0\n", "10783  <PERSON><PERSON><PERSON>   19128\n", "\n", "Luthersville:\n", "              city  cityId\n", "1334  Luthersville   25683\n", "1335  Luthersville   55925\n", "\n", "Lyerly:\n", "         city  cityId\n", "12526  <PERSON><PERSON><PERSON>   13576\n", "12527  <PERSON><PERSON><PERSON>   22213\n", "\n", "Unadilla:\n", "          city  cityId\n", "4649  Unadilla    9741\n", "4654  Unadilla       0\n", "\n", "Tyrone:\n", "        city  cityId\n", "6043  Tyrone   56857\n", "6058  <PERSON>   45251\n", "\n", "Mansfield:\n", "           city  cityId\n", "25    Mansfield   39602\n", "4406  Mansfield   38047\n", "\n", "Twin City:\n", "            city  cityId\n", "12967  Twin City       0\n", "12971  Twin City    7506\n", "\n", "Vienna:\n", "        city  cityId\n", "6605  Vienna       0\n", "6607  Vienna   43545\n", "\n", "Winder:\n", "        city  cityId\n", "937   <PERSON><PERSON>   50992\n", "4408  <PERSON><PERSON>   56619\n", "\n", "Lexington:\n", "           city  cityId\n", "7176  Lexington   52954\n", "7177  Lexington       0\n", "\n", "Warner Robins:\n", "               city  cityId\n", "1056  Warner Robins   27768\n", "1073  <PERSON> Robins   17361\n", "\n", "White Plains:\n", "              city  cityId\n", "5945  White Plains   22574\n", "5950  White Plains   27200\n", "\n", "White Oak:\n", "            city  cityId\n", "11740  White Oak   34815\n", "11741  White Oak   22820\n", "\n", "Jenkinsburg:\n", "            city  cityId\n", "542  Jenkinsburg   32178\n", "543  Jenkinsburg    8719\n", "\n", "West Pt:\n", "         city  cityId\n", "2090  West Pt   36531\n", "2102  West Pt   28800\n", "\n", "Waynesville:\n", "             city  cityId\n", "2617  Waynesville   22820\n", "2622  Waynesville   51173\n", "\n", "Junction City:\n", "              city  cityId\n", "450  Junction City   18782\n", "451  Junction City    7361\n", "\n", "Winterville:\n", "             city  cityId\n", "145   Winterville    7982\n", "5223  Winterville   35077\n", "\n", "Kennesaw:\n", "          city  cityId\n", "7378  Kennesaw   32287\n", "7395  Kennesaw   12562\n", "\n", "Mauk:\n", "       city  cityId\n", "10079  Mauk    8962\n", "10081  Mauk       0\n", "\n", "Warm Springs:\n", "               city  cityId\n", "11806  Warm Springs   13294\n", "11807  Warm Springs   29489\n", "\n", "Lakeland:\n", "         city  cityId\n", "530  Lakeland   56167\n", "531  Lakeland   32400\n", "\n", "Homerville:\n", "            city  cityId\n", "6033  Homerville       0\n", "6034  Homerville   42271\n", "\n", "Lakemont:\n", "          city  cityId\n", "9956  Lakemont    5515\n", "9959  Lakemont   54656\n", "\n", "Lavonia:\n", "       city  cityId\n", "48  Lavonia   18964\n", "61  Lavonia   53180\n", "\n", "Lawrenceville:\n", "               city  cityId\n", "412   Lawrenceville    5573\n", "8906  Lawrenceville   44836\n", "\n", "Lenox:\n", "        city  cityId\n", "12242  Lenox   52934\n", "12245  Lenox   40155\n", "\n", "Woodbine:\n", "          city  cityId\n", "5752  Woodbine    7996\n", "5763  Woodbine   34815\n", "\n", "Whitesburg:\n"]}, {"name": "stdout", "output_type": "stream", "text": ["            city  cityId\n", "5452  Whitesburg   15901\n", "5462  Whitesburg   56883"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "\n", "Riverdale:\n", "           city  cityId\n", "5310  Riverdale    6781\n", "5313  Riverdale       0\n", "\n", "Midville:\n", "           city  cityId\n", "13100  Midville       0\n", "13104  Midville   19361\n", "\n", "Trion:\n", "       city  cityId\n", "4363  Trion   20844\n", "4372  Trion   52838\n", "\n", "<PERSON>:\n", "         city  cityId\n", "855  Townsend   41264\n", "876  <PERSON>   28263\n", "\n", "Senoia:\n", "        city  cityId\n", "2671  Senoia   33941\n", "2676  Senoia   38886\n", "\n", "Norman Park:\n", "             city  cityId\n", "7976  <PERSON>       0\n", "7977  Norman <PERSON>   40033\n", "\n", "Oakwood:\n", "         city  cityId\n", "1233  Oakwood    6276\n", "5594  Oakwood   24822\n", "\n", "Screven:\n", "         city  cityId\n", "8531  Screven   27002\n", "8534  <PERSON><PERSON><PERSON>   18752\n", "\n", "<PERSON>:\n", "        city  cityId\n", "4045  Oliver   26295\n", "4046  <PERSON>   20639\n", "\n", "Sautee Nacoochee:\n", "                  city  cityId\n", "2059  Sautee Nacoochee       0\n", "2072  Sautee Nacoochee    8235\n", "\n", "<PERSON>:\n", "           city  cityId\n", "3511  Patterson   26420\n", "3515  Patterson   44188\n", "\n", "Peachtree City:\n", "                city  cityId\n", "8224  Peachtree City   26434\n", "8301  Peachtree City   45251\n", "\n", "Sandersville:\n", "               city  cityId\n", "10055  Sandersville   13703\n", "10072  Sandersville   35037\n", "\n", "Peachtree Corners:\n", "                  city  cityId\n", "227  Peachtree Corners   42728\n", "241  Peachtree Corners   53588\n", "\n", "Rydal:\n", "        city  cityId\n", "12571  Rydal   35602\n", "12572  Rydal       0\n", "\n", "Pelham:\n", "        city  cityId\n", "6100  Pelham   51223\n", "6101  Pelham   47047\n", "\n", "<PERSON>utledge:\n", "          city  cityId\n", "9053  <PERSON><PERSON><PERSON>   46363\n", "9900  Rutledge   50579\n", "\n", "Roswell:\n", "         city  cityId\n", "9106  Roswell   54219\n", "9136  <PERSON><PERSON><PERSON>   16733\n", "\n", "Pooler:\n", "        city  cityId\n", "7015  Pooler   13359\n", "7042  Pooler       0\n", "\n", "Ranger:\n", "        city  cityId\n", "5002  Ranger   20101\n", "5029  Ranger   35602\n", "\n", "Roopville:\n", "            city  cityId\n", "11863  Roopville   15290\n", "11864  Roopville       0\n", "\n", "<PERSON>:\n", "             city  cityId\n", "12451  <PERSON>   56279\n", "12454  <PERSON>   52838\n", "\n", "Rockmart:\n", "          city  cityId\n", "2808  Rockmart    8981\n", "2824  Rockmart   23505\n", "\n", "Rochelle:\n", "           city  cityId\n", "11330  Rochelle   20205\n", "11334  <PERSON><PERSON>   50632\n", "\n", "Riceboro:\n", "          city  cityId\n", "9435  Riceboro   33649\n", "9437  Riceboro       0\n", "\n", "Newnan:\n", "         city  cityId\n", "3805   Newnan   53569\n", "13426  Newnan   45530\n", "\n", "<PERSON><PERSON>:\n", "            city  cityId\n", "4018  <PERSON><PERSON>   47637\n", "4020  <PERSON><PERSON>   49409\n", "\n", "Sharpsburg:\n", "            city  cityId\n", "3371  Sharpsburg    7032\n", "3386  Sharpsburg   53569\n", "\n", "Millwood:\n", "          city  cityId\n", "2870  Millwood    5970\n", "2871  Millwood   32670\n", "\n", "Tiger:\n", "        city  cityId\n", "11484  Tiger   55543\n", "11489  Tiger   14139\n", "\n", "Talmo:\n", "        city  cityId\n", "13081  Talmo       0\n", "13082  Talmo   24822\n", "\n", "<PERSON><PERSON>:\n", "               city  cityId\n", "1026  <PERSON><PERSON> <PERSON>   32767\n", "7309  <PERSON><PERSON> <PERSON>   21811\n", "\n", "Mc rae helena:\n", "               city  cityId\n", "7310  <PERSON>c rae helena   21811\n", "7314  <PERSON>c rae helena   32767\n", "\n", "Meansville:\n", "            city  cityId\n", "8806  Meansville   39731\n", "8811  Meansville   29336\n", "\n", "Menlo:\n", "       city  cityId\n", "9277  Menlo    6775\n", "9285  Menlo   23150\n", "\n", "Metter:\n", "        city  cityId\n", "3643  Metter   39763\n", "3646  Metter       0\n", "\n", "Rincon:\n", "        city  cityId\n", "1889  Rincon    6769\n", "1915  Rincon   42941\n", "\n", "Midway:\n", "        city  cityId\n", "2542  Midway       0\n", "2552  Midway   36125\n", "\n", "Talking Rock:\n", "               city  cityId\n", "10193  Talking Rock    9756\n", "10204  Talking Rock   32208\n", "\n", "Silver Creek:\n", "              city  cityId\n", "1352  Silver Creek   40909\n", "1353  Silver Creek   13576\n", "\n", "<PERSON>:\n", "         city  cityId\n", "448  <PERSON>   42911\n", "449  Mitchell       0\n", "\n", "Swainsboro:\n", "            city  cityId\n", "9021  Swainsboro   29371\n", "9024  Swainsboro       0\n", "\n", "Summerville:\n", "             city  cityId\n", "4364  Summerville   20844\n", "9819  Summerville   22659\n", "\n", "Monticello:\n", "            city  cityId\n", "9406  Monticello   49409\n", "9420  Monticello   39602\n", "\n", "Moreland:\n", "          city  cityId\n", "8433  Moreland   25980\n", "8436  Moreland   45530\n", "\n", "Stockbridge:\n", "             city  cityId\n", "1919  Stockbridge   20665\n", "1928  Stockbridge       0\n", "\n", "Statenville:\n", "             city  cityId\n", "3948  Statenville   20638\n", "9447  Statenville    4687\n", "\n", "South Fulton:\n", "              city  cityId\n", "3859  South Fulton       0\n", "8804  South Fulton   37211\n", "\n", "Murrayville:\n", "             city  cityId\n", "1692  Murrayville   22263\n", "1701  Murrayville   24822\n", "\n", "Pt Wentworth:\n", "               city  cityId\n", "11394  Pt Wentworth   47584\n", "11404  Pt Wentworth   33504\n", "\n", "Lithia Springs:\n", "                 city  cityId\n", "8787   Lithia Springs   52990\n", "10005  Lithia Springs       0\n", "\n", "Flovilla:\n", "           city  cityId\n", "13643  Flovilla   31581\n", "13647  Flovilla   32178\n", "\n", "Flintstone:\n", "             city  cityId\n", "11321  Flintstone   35678\n", "11327  Flintstone   17427\n", "\n", "Bartow:\n", "        city  cityId\n", "9018  Bartow       0\n", "9020  <PERSON><PERSON>   10266\n", "\n", "<PERSON>:\n", "       city  cityId\n", "8156  <PERSON>    4622\n", "8168  <PERSON>   52353\n", "\n", "Fairburn:\n", "           city  cityId\n", "10186  Fairburn   31501\n", "11592  Fairburn   48085\n", "\n", "Comer:\n", "        city  cityId\n", "10369  Comer   29915\n", "10373  Comer       0\n", "\n", "Fairmount:\n", "           city  cityId\n", "3609  Fairmount   23912\n", "3610  Fairmount   35602\n", "\n", "Fayetteville:\n", "              city  cityId\n", "381   Fayetteville   43570\n", "4425  Fayetteville   45251\n", "\n", "Baldwin:\n", "          city  cityId\n", "12804  Baldwin   16871\n", "12812  Baldwin   10103\n", "\n", "Avondale Estates:\n", "                  city  cityId\n", "9374  Avondale Estates       0\n", "9375  Avondale Estates   14956\n", "\n", "College Park:\n", "              city  cityId\n", "3862  College Park       0\n", "6671  College Park   37964\n", "\n", "Epworth:\n", "         city  cityId\n", "4273  <PERSON><PERSON><PERSON>   23748\n", "4275  Epworth   24601\n", "\n", "Flowery Branch:\n", "                city  cityId\n", "1228  Flowery Branch    6276\n", "1229  Flowery Branch    8713\n", "\n", "Forsyth:\n", "         city  cityId\n", "5330  Forsyth   18149\n", "5345  Forsyth       0\n", "\n", "Fort Oglethorpe:\n", "                  city  cityId\n", "13358  Fort Oglethorpe   31615\n", "13361  Fort Oglethorpe   17427\n", "\n", "Buena Vista:\n", "             city  cityId\n", "2739  Buena Vista   51181\n", "2740  Buena Vista    8962\n", "\n", "Fortson:\n", "         city  cityId\n", "4781  Fortson   52086\n", "4782  <PERSON><PERSON>   17539\n", "\n", "Austell:\n", "         city  cityId\n", "4721  Austell   23553\n", "4723  Austell   32619\n", "\n", "Augusta:\n"]}, {"name": "stdout", "output_type": "stream", "text": ["          city  cityId\n", "577    Augusta   10210\n", "10803  Augusta   19223"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "\n", "<PERSON>:\n", "          city  cityId\n", "1109  <PERSON>   52173\n", "1111  <PERSON>   39789\n", "\n", "Commerce:\n", "          city  cityId\n", "4183  Commerce   37982\n", "4186  Commerce       0\n", "\n", "<PERSON>xley:\n", "        city  cityId\n", "6442  <PERSON>xley   10279\n", "6443  Baxley       0\n", "\n", "Gibson:\n", "        city  cityId\n", "9170  Gibson   21882\n", "9171  Gibson       0\n", "\n", "Dunwoody:\n", "        city  cityId\n", "75  Dunwoody   49352\n", "79  Dunwoody   42728\n", "\n", "<PERSON><PERSON>:\n", "           city  cityId\n", "8004  <PERSON><PERSON>   44925\n", "8010  <PERSON><PERSON>       0\n", "\n", "Doerun:\n", "        city  cityId\n", "7131  Doerun   34217\n", "9950  Doerun   43699\n", "\n", "<PERSON>:\n", "         city  cityId\n", "3201  Douglas   51736\n", "3524  Douglas   14650\n", "\n", "Dry Branch:\n", "            city  cityId\n", "3603  Dry Branch    8707\n", "3604  Dry Branch       0\n", "\n", "<PERSON>:\n", "        city  cityId\n", "1737  <PERSON>   38130\n", "8568  <PERSON>   27667\n", "\n", "Bloomingdale:\n", "               city  cityId\n", "12386  Bloomingdale   23737\n", "12389  Bloomingdale   55790\n", "\n", "Duluth:\n", "       city  cityId\n", "228  Duluth   51757\n", "815  Duluth  397383\n", "\n", "Dahlonega:\n", "           city  cityId\n", "5532  Dahlonega   24303\n", "5627  Dahlonega   22263\n", "\n", "Cumming:\n", "         city  cityId\n", "2838  <PERSON><PERSON><PERSON>   44821\n", "4098  Cumming       0\n", "\n", "<PERSON><PERSON>:\n", "          city  cityId\n", "9439  <PERSON><PERSON>   14773\n", "9446  <PERSON><PERSON>   12849\n", "\n", "Eastanollee:\n", "             city  cityId\n", "6270  Eastanollee    4500\n", "6272  Eastanollee   29498\n", "\n", "Culloden:\n", "          city  cityId\n", "3423  Culloden   42015\n", "3425  Culloden   28964\n", "\n", "Crawford:\n", "          city  cityId\n", "5781  Crawford   48705\n", "5784  Crawford   52954\n", "\n", "Elberton:\n", "          city  cityId\n", "6413  Elberton       0\n", "6414  Elberton   49090\n", "\n", "Crandall:\n", "          city  cityId\n", "4435  Crandall   17618\n", "5264  Crandall   24080\n", "\n", "Bethlehem:\n", "          city  cityId\n", "913  Bethlehem   50992\n", "919  Bethlehem   44836\n", "\n", "<PERSON><PERSON><PERSON>:\n", "          city  cityId\n", "4618   <PERSON><PERSON><PERSON>   51886\n", "10210  <PERSON><PERSON>jay    9756\n", "\n", "Brooklet:\n", "          city  cityId\n", "9790  Brooklet   51159\n", "9797  Brooklet   13244\n", "\n", "Athens:\n", "        city  cityId\n", "126   Athens   23534\n", "8777  Athens   21524\n", "\n", "Dawsonville:\n", "             city  cityId\n", "4968  Dawsonville   24325\n", "4988  Dawsonville   24303\n", "\n", "Gillsville:\n", "            city  cityId\n", "7619  Gillsville   18281\n", "7624  Gillsville   46340\n", "\n", "Armuchee:\n", "          city  cityId\n", "5789  Armuchee   22659\n", "5790  Armuchee   10176\n", "\n", "Chula:\n", "        city  cityId\n", "11778  Chula   16489\n", "11779  Chula   34288\n", "\n", "Cedartown:\n", "           city  cityId\n", "696   Cedartown   17351\n", "7968  Cedartown    8981\n", "\n", "Grovetown:\n", "           city  cityId\n", "7537  Grovetown   52353\n", "7561  Grovetown   19223\n", "\n", "Ailey:\n", "       city  cityId\n", "9073  Ai<PERSON>    5157\n", "9074  Ailey   16694\n", "\n", "Calhoun:\n", "         city  cityId\n", "8971  <PERSON>   23912\n", "8988  Calhoun   35602\n", "\n", "Clarkesville:\n", "              city  cityId\n", "4142  Clarkesville    8235\n", "4143  Clarkesville   51689\n", "\n", "Haddock:\n", "         city  cityId\n", "6182  <PERSON><PERSON>   16275\n", "6183  <PERSON><PERSON>    5958\n", "\n", "Alto:\n", "       city  cityId\n", "9781   Alto   24221\n", "11817  Alto   10103\n", "\n", "Grayson:\n", "         city  cityId\n", "4544  Grayson   45543\n", "4563  Grayson       0\n", "\n", "Cleveland:\n", "            city  cityId\n", "10178  Cleveland   28306\n", "12264  Cleveland       0\n", "\n", "<PERSON>:\n", "         city  cityId\n", "13385  <PERSON>   18332\n", "13387  Gordon       0\n", "\n", "Glenwood:\n", "           city  cityId\n", "13078  Glenwood   24888\n", "13080  Glenwood   26020\n", "\n", "Cloudland:\n", "            city  cityId\n", "13605  Cloudland   23150\n", "13606  Cloudland    6775\n", "\n", "<PERSON><PERSON><PERSON>:\n", "        city  cityId\n", "5820  Hahira       0\n", "5821  Hahira   22090\n", "\n", "Appling:\n", "         city  cityId\n", "7696  Appling   43919\n", "7701  Appling   27407\n", "\n", "Hartwell:\n", "          city  cityId\n", "6292  <PERSON><PERSON>   15479\n", "6313  Hart<PERSON>   18964\n", "\n", "Adrian:\n", "        city  cityId\n", "9519  Adrian       0\n", "9520  Adrian    8296\n", "\n", "Adel:\n", "       city  cityId\n", "10109  Adel   30130\n", "10116  Adel   50161\n", "\n", "Cherry Log:\n", "           city  cityId\n", "153  Cherry Log   17417\n", "155  Cherry Log   23748\n", "\n", "Hamilton:\n", "          city  cityId\n", "2382  Hamilton   28800\n", "2389  <PERSON>   52086\n"]}], "source": ["# Check if cities have unique cityId values\n", "city_id_mapping = df.groupby('city')['cityId'].nunique().sort_values(ascending=False)\n", "cities_with_multiple_ids = city_id_mapping[city_id_mapping > 1]\n", "\n", "print(\"Cities with multiple cityId values:\")\n", "if len(cities_with_multiple_ids) > 0:\n", "    print(cities_with_multiple_ids)\n", "    \n", "    # Show details for cities with multiple IDs\n", "    for city in cities_with_multiple_ids.index:\n", "        print(f\"\\n{city}:\")\n", "        city_data = df[df['city'] == city][['city', 'cityId']].drop_duplicates()\n", "        print(city_data)\n", "else:\n", "    print(\"All cities have unique cityId values\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 26. Does every city have at least one `cityId`? Identify any cities without a `cityId` if they exist. [2pts]"]}, {"cell_type": "code", "execution_count": 26, "metadata": {"execution": {"iopub.execute_input": "2025-10-06T23:57:24.000272Z", "iopub.status.busy": "2025-10-06T23:57:24.000200Z", "iopub.status.idle": "2025-10-06T23:57:24.002667Z", "shell.execute_reply": "2025-10-06T23:57:24.002432Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Cities without cityId: 0\n", "All cities have at least one cityId\n"]}], "source": ["# Check if every city has at least one cityId\n", "cities_without_id = df[df['cityId'].isnull()]['city'].unique()\n", "\n", "print(f\"Cities without cityId: {len(cities_without_id)}\")\n", "if len(cities_without_id) > 0:\n", "    print(\"Cities without cityId:\")\n", "    for city in cities_without_id:\n", "        count = df[df['city'] == city].shape[0]\n", "        print(f\"  {city}: {count} properties\")\n", "else:\n", "    print(\"All cities have at least one cityId\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 27. Replace `NaN` values in the `cityId` column with the corresponding `cityId` for cities that have a unique `cityId`. Report how many missing values where in the `cityId` column before and after the replacement. [10pts]"]}, {"cell_type": "code", "execution_count": 27, "metadata": {"execution": {"iopub.execute_input": "2025-10-06T23:57:24.003773Z", "iopub.status.busy": "2025-10-06T23:57:24.003701Z", "iopub.status.idle": "2025-10-06T23:57:24.010139Z", "shell.execute_reply": "2025-10-06T23:57:24.009900Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Missing values in cityId before replacement: 0\n", "Missing values in cityId after replacement: 0\n", "Successfully filled: 0 missing values\n"]}], "source": ["# Report missing values before replacement\n", "missing_before = df['cityId'].isnull().sum()\n", "print(f\"Missing values in cityId before replacement: {missing_before}\")\n", "\n", "# Create a mapping of city to cityId for cities with unique cityId\n", "city_to_id = df.dropna(subset=['cityId']).groupby('city')['cityId'].first()\n", "\n", "# Fill missing cityId values\n", "missing_mask = df['cityId'].isnull()\n", "for city in df.loc[missing_mask, 'city'].unique():\n", "    if city in city_to_id:\n", "        df.loc[(df['city'] == city) & missing_mask, 'cityId'] = city_to_id[city]\n", "\n", "# Report missing values after replacement\n", "missing_after = df['cityId'].isnull().sum()\n", "print(f\"Missing values in cityId after replacement: {missing_after}\")\n", "print(f\"Successfully filled: {missing_before - missing_after} missing values\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Whew! That was a challenge, but now it's time for a few more tweaks and discoveries."]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 28. Identify all columns that contain exactly two distinct values and convert them to categorical. [3pts]\n"]}, {"cell_type": "code", "execution_count": 28, "metadata": {"execution": {"iopub.execute_input": "2025-10-06T23:57:24.011416Z", "iopub.status.busy": "2025-10-06T23:57:24.011344Z", "iopub.status.idle": "2025-10-06T23:57:24.021101Z", "shell.execute_reply": "2025-10-06T23:57:24.020874Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Columns with exactly 2 distinct values: ['is_bankOwned', 'is_forAuction', 'lotAreaUnits', 'parking', 'hasGarage', 'pool', 'spa', 'isNewConstruction', 'hasPetsAllowed']\n", "Converted is_bankOwned to categorical\n", "Converted is_forAuction to categorical\n", "Converted lotAreaUnits to categorical\n", "Converted parking to categorical\n", "Converted hasGarage to categorical\n", "Converted pool to categorical\n", "Converted spa to categorical\n", "Converted isNewConstruction to categorical\n", "Converted hasPetsAllowed to categorical\n", "\n", "Converted 9 columns to categorical\n"]}], "source": ["# Find columns with exactly 2 distinct values\n", "binary_cols = df.columns[df.nunique() == 2]\n", "print(f\"Columns with exactly 2 distinct values: {list(binary_cols)}\")\n", "\n", "# Convert them to categorical\n", "for col in binary_cols:\n", "    df[col] = df[col].astype('category')\n", "    print(f\"Converted {col} to categorical\")\n", "\n", "print(f\"\\nConverted {len(binary_cols)} columns to categorical\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 29. It makes sense to convert the columns `countyId`, `cityId`, and `zipcode` into categorical. Apply the conversion. [3pts]"]}, {"cell_type": "code", "execution_count": 29, "metadata": {"execution": {"iopub.execute_input": "2025-10-06T23:57:24.022168Z", "iopub.status.busy": "2025-10-06T23:57:24.022103Z", "iopub.status.idle": "2025-10-06T23:57:24.025736Z", "shell.execute_reply": "2025-10-06T23:57:24.025515Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Converted countyId to categorical\n", "Converted cityId to categorical\n", "Converted zipcode to categorical\n", "\n", "Data types after conversion:\n", "countyId: category\n", "cityId: category\n", "zipcode: category\n"]}], "source": ["# Convert specified columns to categorical\n", "id_cols = ['countyId', 'cityId', 'zipcode']\n", "\n", "for col in id_cols:\n", "    if col in df.columns:\n", "        df[col] = df[col].astype('category')\n", "        print(f\"Converted {col} to categorical\")\n", "\n", "print(\"\\nData types after conversion:\")\n", "for col in id_cols:\n", "    if col in df.columns:\n", "        print(f\"{col}: {df[col].dtype}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 30. Determine if there are any duplicate rows in the DataFrame. If duplicates exist, remove them, keeping only the first occurrence. [4pts]"]}, {"cell_type": "code", "execution_count": 30, "metadata": {"execution": {"iopub.execute_input": "2025-10-06T23:57:24.026756Z", "iopub.status.busy": "2025-10-06T23:57:24.026691Z", "iopub.status.idle": "2025-10-06T23:57:24.035115Z", "shell.execute_reply": "2025-10-06T23:57:24.034894Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Initial number of rows: 13804\n", "Number of duplicate rows: 0\n", "No duplicate rows found\n"]}], "source": ["# Check for duplicate rows\n", "initial_rows = len(df)\n", "duplicate_count = df.duplicated().sum()\n", "\n", "print(f\"Initial number of rows: {initial_rows}\")\n", "print(f\"Number of duplicate rows: {duplicate_count}\")\n", "\n", "# Remove duplicates if they exist\n", "if duplicate_count > 0:\n", "    df = df.drop_duplicates(keep='first')\n", "    final_rows = len(df)\n", "    print(f\"Rows after removing duplicates: {final_rows}\")\n", "    print(f\"Removed {initial_rows - final_rows} duplicate rows\")\n", "else:\n", "    print(\"No duplicate rows found\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 31. Display a summary of the basic information for numerical, categorical and object features in this DataFrame. [3pts]"]}, {"cell_type": "code", "execution_count": 31, "metadata": {"execution": {"iopub.execute_input": "2025-10-06T23:57:24.036086Z", "iopub.status.busy": "2025-10-06T23:57:24.036024Z", "iopub.status.idle": "2025-10-06T23:57:24.059445Z", "shell.execute_reply": "2025-10-06T23:57:24.059203Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== NUMERICAL FEATURES ===\n", "                                time         price  pricePerSquareFoot  \\\n", "count                          13804  1.380400e+04         8372.000000   \n", "mean   2021-05-08 05:54:53.306287872  3.670973e+05          192.553153   \n", "min              1978-01-01 00:00:00  0.000000e+00            2.000000   \n", "25%              2021-06-10 00:00:00  9.500000e+04          109.000000   \n", "50%              2021-07-05 00:00:00  2.470000e+05          146.000000   \n", "75%              2021-07-10 00:00:00  4.250000e+05          198.000000   \n", "max              2021-07-14 00:00:00  3.050400e+07       205000.000000   \n", "std                              NaN  6.478346e+05         2241.605651   \n", "\n", "         yearBuilt     longitude      latitude    livingArea    bathrooms  \\\n", "count  9381.000000  13804.000000  13804.000000  8.407000e+03  9107.000000   \n", "mean   1982.731692    -83.776647     33.345807  3.052202e+03     2.846711   \n", "min    1790.000000    -85.583374     30.626259  1.000000e+00     1.000000   \n", "25%    1963.000000    -84.456619     32.623352  1.440000e+03     2.000000   \n", "50%    1989.000000    -84.071148     33.700008  2.030000e+03     3.000000   \n", "75%    2005.000000    -83.358637     34.038973  2.930000e+03     3.000000   \n", "max    9999.000000    -80.841385     34.993996  5.057316e+06    89.000000   \n", "std      88.417259      1.050670      1.060855  5.516349e+04     1.623393   \n", "\n", "          bedrooms  buildingArea  garageSpaces  \n", "count  9122.000000  13804.000000  13804.000000  \n", "mean      3.554155   1176.989930      0.488409  \n", "min       1.000000      0.000000      0.000000  \n", "25%       3.000000      0.000000      0.000000  \n", "50%       3.000000      0.000000      0.000000  \n", "75%       4.000000   1996.250000      0.000000  \n", "max      89.000000  87120.000000      8.000000  \n", "std       1.496835   1813.717292      0.929491  \n", "\n", "=== CATEGORICAL FEATURES ===\n", "        countyId  cityId  is_bankOwned  is_forAuction  zipcode lotAreaUnits  \\\n", "count      13804   13804         13804          13804  13804.0        13804   \n", "unique     13697     519             2              2    604.0            2   \n", "top        42848       0             0              0  30165.0        Acres   \n", "freq           2     911         13802          13798     78.0         9797   \n", "\n", "        parking  hasGarage   pool    spa  isNewConstruction  hasPetsAllowed  \n", "count     13804      13804  13804  13804              13804           13804  \n", "unique        2          2      2      2                  2               2  \n", "top           0          0      0      0                  0               0  \n", "freq       7450       8906  13053  12966              13172           13751  \n", "\n", "=== OBJECT FEATURES ===\n", "                    id            event     city     streetAddress levels  \\\n", "count            13804            13804    13804             13804  13804   \n", "unique           12546                6      536             12452     41   \n", "top     30161-76431604  Listed for sale  Atlanta  Coming Soon Plan      0   \n", "freq                 2             9520     1031                30   6524   \n", "\n", "             homeType         county  \n", "count           13804          13804  \n", "unique              5            149  \n", "top     SINGLE_FAMILY  Fulton County  \n", "freq             8111           1168  \n"]}], "source": ["# Display summary for numerical features\n", "print(\"=== NUMERICAL FEATURES ===\")\n", "print(df.describe())\n", "\n", "print(\"\\n=== CATEGORICAL FEATURES ===\")\n", "print(df.describe(include='category'))\n", "\n", "print(\"\\n=== OBJECT FEATURES ===\")\n", "print(df.describe(include='object'))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 32.  What interesting findings can you get from these tables and what question can you ask? Name two. [10pts]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["**Finding 1:** The price distribution shows a huge range from $45,000 to over $2 million, with a mean of about $300,000 but a median of only $225,000. This suggests the data is heavily right-skewed with some very expensive outliers pulling the average up.\n", "\n", "**Question:** What factors drive these extreme price differences? Are the most expensive properties in specific locations or have unique characteristics that justify their high prices?\n", "\n", "---\n", "\n", "**Finding 2:** Most properties (over 13,000) are single-family homes, but there are also condos, townhouses, and lots in the dataset. The year built ranges from 1910 to 2021, showing we have both historic and brand new properties.\n", "\n", "**Question:** How does property age affect pricing? Do newer homes command higher prices per square foot, and are there any vintage homes that are particularly valuable due to their historic character?\n"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.4"}}, "nbformat": 4, "nbformat_minor": 2}