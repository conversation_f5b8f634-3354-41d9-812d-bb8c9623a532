{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Homework 2: Preliminary EDA (Exploratory Data Analysis)\n", "\n", "**Course:** 4780/6780 Fundamentals of Data Science  \n", "**Instructor:** <PERSON><PERSON>  \n", "**Due:** 11pm, Monday, October 6, 2025\n", "\n", "---\n", "\n", "## How to use this notebook\n", "- Work top-to-bottom. Run each cell after you edit it.\n", "- Keep your answers clear and short.\n", "- Do not delete the problem statements.\n", "- You may add cells as needed.\n", "- When finished, run *Kernel → Restart & Run All*, verify outputs, then submit.\n", "\n", "\n", "**Honor code:** Write your own code. You may discuss high-level ideas with peers, but all implementation must be yours.\n", "\n", "---"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## The task\n", "Imagine you are a data scientist at a real estate company. Your task is to analyze Atlanta's real estate data. Start by inspecting the dataset *real_estate_ga.csv*, which contains property information from 2021. Familiarize yourself with its structure and contents before proceeding with EDA.\n", "\n", "**Requirements:**\n", "\n", "- Please use [pandas.DataFrame](http://pandas.pydata.org/pandas-docs/stable/generated/pandas.DataFrame.html) to manipulate data.\n", "\n", "- Please follow the python code style (https://www.python.org/dev/peps/pep-0008/). If TA finds your code hard to read, you will lose points. This requirement will stay for the whole semester."]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import pandas as pd"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 1. First, load the data as a DataFrame. \n", "*You don't need to do anything for this task as it has already been completed. Simply run cells.*"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["df = pd.read_csv(\"real_estate_ga.csv\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 2. Display the first 4 rows of the DataFrame `df`. [2pts]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 3. Display the 3 random rows of the DataFrame `df`. [2pts]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 4. Display the 2 random rows of the DataFrame `df`. Make sure that the rows stay the same if one reruns this cell. [2pts]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 5. How many row and columns are in the dataset? [2pts]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 6. Print list of column names. [2pts]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "metadata": {}, "source": ["Ensure you understand the meaning of each column! This may require getting hands-on with the data - exploring it thoroughly and even opening the CSV file in Excel for a clearer view."]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 7. What is `Unnamed: 0` column? Can we delete it without losing essential information? Why? [3pts]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["*Write your answer in this cell. Delete this text and type your answer instead.*"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 8. Delete column `Unnamed: 0`. [2pts]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 9. Display a summary of the basic information for the numerical features in this DataFrame. [2pts]"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["pd.options.display.max_columns = None # You may want to this option to display all columns"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Review the summary table above and ensure you understand the meaning of each row (e.g., standard deviation, 25th percentile). Then, carefully examine each column to grasp what is happening in the data."]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 10. Display a summary of the basic information for the categorical features in this DataFrame. [2pts]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "metadata": {}, "source": ["Review the summary table above and ensure you understand the meaning of each row (e.g., unique, top, freq). Then, carefully examine each column to grasp what is happening in the data."]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 11. Print number of unique values in each column of the DataFrame. [2pts]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 12. Some columns contain only a single unique value. Which ones? Print their names. Then, delete all such columns and explain why removing them makes sense. [3pts]\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "metadata": {}, "source": ["*Write your answer in this cell. Delete this text and type your answer instead.*"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 13. Run the code cell below. Are the results the same or different? Whether they differ or not differ, explain why. [2pts]"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["True\n", "True\n"]}], "source": ["print((df['livingArea'] == df['livingAreaValue']).all())\n", "print(df['livingArea'].equals(df['livingAreaValue']))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["*Write your answer in this cell. Delete this text and type your answer instead.*"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 14. Display a summary of the numerical features again. Identify any columns with identical statistics. If such columns exist, verify whether they are true duplicates and keep only the first occurrence, removing all others. [3pts]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 15. Identify all columns that contain exactly two unique values in the DataFrame and retrieve the unique values present in each of them.\n", "*You don't need to do anything for this task as it has already been completed. Simply run cells.*"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>is_bankOwned</th>\n", "      <th>is_forAuction</th>\n", "      <th>lotAreaUnits</th>\n", "      <th>parking</th>\n", "      <th>hasGarage</th>\n", "      <th>pool</th>\n", "      <th>spa</th>\n", "      <th>isNewConstruction</th>\n", "      <th>hasPetsAllowed</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td><PERSON><PERSON>s</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>sqft</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   is_bankOwned  is_forAuction lotAreaUnits  parking  hasGarage  pool  spa  \\\n", "0             0              0        Acres        0          0     0    0   \n", "1             1              1         sqft        1          1     1    1   \n", "\n", "   isNewConstruction  hasPetsAllowed  \n", "0                  0               0  \n", "1                  1               1  "]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["df.loc[:, df.nunique() == 2].apply(pd.unique)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Learn how `.apply()` method works!"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 16. Something is wrong with `time`. [8pts]\n", "\n", "The column `time` contains strange numbers. Convert it into a readable datetime format. Keep in mind that the dataset contains property information from 2021.  \n", "\n", "Refer to the [Pandas documentation](https://pandas.pydata.org/pandas-docs/stable/reference/api/pandas.to_datetime.html) for `to_datetime()` for guidance. Play with the parameter `unit`."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 17. Is there another column contains the same time-related information. [2pts]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["*Write your answer in this cell. Delete this text and type your answer instead.*"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 18. Let's check for missing values in each column [4pts]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 19. Can the missing values in the `time` column be inferred from other available data? If so, fill in the missing values appropriately. [8pts]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 20. Delete columns `datePostedString` and `description` [2pts]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 21. Print rows where any value is missing, displaying only the columns: `event`, `time`, and `city`. [2pts]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 22. Change all values `NaN` in the `event` column to `Unknown` [3pts]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 23. Do we still have missing values? [3pts]\n", "\n", "It appears that we no longer have any explicit missing values (`NaN`). However, missing data can sometimes be represented by zeros. Identify all columns where this might be the case."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 24. In all those columns replace zeros with `NaN`s [2pts]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Can we reliably infer missing `cityId` values using `city`?\n", "\n", "To answer such question, we need to understand a few things."]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 25. Do all cities have unique `cityId`? Find cities wuth multiple IDs if any. [2pts]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 26. Does every city have at least one `cityId`? Identify any cities without a `cityId` if they exist. [2pts]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 27. Replace `NaN` values in the `cityId` column with the corresponding `cityId` for cities that have a unique `cityId`. Report how many missing values where in the `cityId` column before and after the replacement. [10pts]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Whew! That was a challenge, but now it's time for a few more tweaks and discoveries."]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 28. Identify all columns that contain exactly two distinct values and convert them to categorical. [3pts]\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 29. It makes sense to convert the columns `countyId`, `cityId`, and `zipcode` into categorical. Apply the conversion. [3pts]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 30. Determine if there are any duplicate rows in the DataFrame. If duplicates exist, remove them, keeping only the first occurrence. [4pts]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 31. Display a summary of the basic information for numerical, categorical and object features in this DataFrame. [3pts]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 32.  What interesting findings can you get from these tables and what question can you ask? Name two. [10pts]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Below are some examples:\n", "\n", "**Finding 1:**  The binary feature `is_bankOwned` has only two values:\n", "- 0 (Not Bank-Owned): 13,802 properties  \n", "- 1 (Bank-Owned): Only 2 properties  \n", "\n", "**Question:** Why is this the case? Is it due to missing data, dataset bias, or other factors?  \n", "\n", "---\n", "\n", "**Finding 2:**  The feature `pricePerSquareFoot` has an extreme outlier $205,000 per square foot.  \n", "- The total price of this property is also $205,000, which strongly suggests a data entry error.  \n", "\n", "**Question:** Are there any other similar errors in the dataset?\n", "\n", "*Delete the two examples above and write your answer here.*\n"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.5"}}, "nbformat": 4, "nbformat_minor": 2}