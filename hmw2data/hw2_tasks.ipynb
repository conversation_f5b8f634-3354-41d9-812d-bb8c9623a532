import numpy as np
import pandas as pd

df = pd.read_csv("real_estate_ga.csv")

df.head(4)

df.sample(3)

df.sample(2, random_state=42)

print(f"Number of rows: {df.shape[0]}")
print(f"Number of columns: {df.shape[1]}")

print(list(df.columns))

df = df.drop('Unnamed: 0', axis=1)

pd.options.display.max_columns = None # You may want to this option to display all columns

df.describe()

df.describe(include='object')

df.nunique()

# Find columns with only one unique value
single_value_cols = df.columns[df.nunique() == 1]
print("Columns with only one unique value:")
print(list(single_value_cols))

# Drop these columns
df = df.drop(columns=single_value_cols)
print(f"\nDropped {len(single_value_cols)} columns")

print((df['livingArea'] == df['livingAreaValue']).all())
print(df['livingArea'].equals(df['livingAreaValue']))

# Display summary of numerical features
numerical_summary = df.describe()
print(numerical_summary)

# Check for duplicate columns by comparing all values
duplicate_cols = []
cols = df.select_dtypes(include=[np.number]).columns.tolist()

for i in range(len(cols)):
    for j in range(i+1, len(cols)):
        if df[cols[i]].equals(df[cols[j]]):
            duplicate_cols.append(cols[j])
            print(f"Found duplicate columns: {cols[i]} and {cols[j]}")

# Remove duplicate columns
if duplicate_cols:
    df = df.drop(columns=duplicate_cols)
    print(f"\nRemoved duplicate columns: {duplicate_cols}")
else:
    print("\nNo duplicate columns found")

df.loc[:, df.nunique() == 2].apply(pd.unique)

# First, let's examine the time column
print("Sample time values:")
print(df['time'].head())
print(f"\nTime column data type: {df['time'].dtype}")

# Convert time from milliseconds to datetime
df['time'] = pd.to_datetime(df['time'], unit='ms')

print("\nConverted time values:")
print(df['time'].head())

# Check for missing values in each column
missing_values = df.isnull().sum()
print("Missing values per column:")
print(missing_values[missing_values > 0].sort_values(ascending=False))

print(f"\nTotal missing values: {df.isnull().sum().sum()}")
print(f"Percentage of missing values: {(df.isnull().sum().sum() / (df.shape[0] * df.shape[1])) * 100:.2f}%")

# Check if we can fill missing time values from datePostedString
print("Missing values in time column before filling:")
print(df['time'].isnull().sum())

# Check if datePostedString has values where time is missing
missing_time_mask = df['time'].isnull()
print(f"\nRows with missing time: {missing_time_mask.sum()}")

if missing_time_mask.sum() > 0:
    # Check if datePostedString has values for these rows
    print("DatePostedString values where time is missing:")
    print(df.loc[missing_time_mask, 'datePostedString'].head())
    
    # Fill missing time values from datePostedString
    df.loc[missing_time_mask, 'time'] = pd.to_datetime(df.loc[missing_time_mask, 'datePostedString'])
    
    print(f"\nMissing values in time column after filling: {df['time'].isnull().sum()}")
else:
    print("No missing values in time column to fill")

# Delete the specified columns
df = df.drop(columns=['datePostedString', 'description'])
print("Deleted columns: datePostedString and description")
print(f"DataFrame shape after deletion: {df.shape}")

# Find rows with any missing values and display specific columns
rows_with_missing = df[df.isnull().any(axis=1)]
print(f"Number of rows with missing values: {len(rows_with_missing)}")
print("\nRows with missing values (showing event, time, city):")
print(rows_with_missing[['event', 'time', 'city']].head(10))

# Replace NaN values in event column with 'Unknown'
print(f"Missing values in event column before: {df['event'].isnull().sum()}")
df['event'] = df['event'].fillna('Unknown')
print(f"Missing values in event column after: {df['event'].isnull().sum()}")

# Check for remaining missing values
print("Current missing values:")
print(df.isnull().sum().sum())

# Identify columns where zeros might represent missing values
# These are typically columns where 0 doesn't make logical sense
numeric_cols = df.select_dtypes(include=[np.number]).columns
zero_counts = (df[numeric_cols] == 0).sum()

print("\nColumns with zeros (potential missing values):")
suspicious_cols = ['yearBuilt', 'bathrooms', 'bedrooms', 'livingArea', 'pricePerSquareFoot']
for col in suspicious_cols:
    if col in df.columns:
        zero_count = (df[col] == 0).sum()
        print(f"{col}: {zero_count} zeros")

# Replace zeros with NaN in columns where 0 doesn't make sense
cols_to_replace = ['yearBuilt', 'bathrooms', 'bedrooms', 'livingArea', 'pricePerSquareFoot']

for col in cols_to_replace:
    if col in df.columns:
        before_count = (df[col] == 0).sum()
        df[col] = df[col].replace(0, np.nan)
        after_count = df[col].isnull().sum()
        print(f"{col}: Replaced {before_count} zeros with NaN")

print(f"\nTotal missing values after replacement: {df.isnull().sum().sum()}")

# Check if cities have unique cityId values
city_id_mapping = df.groupby('city')['cityId'].nunique().sort_values(ascending=False)
cities_with_multiple_ids = city_id_mapping[city_id_mapping > 1]

print("Cities with multiple cityId values:")
if len(cities_with_multiple_ids) > 0:
    print(cities_with_multiple_ids)
    
    # Show details for cities with multiple IDs
    for city in cities_with_multiple_ids.index:
        print(f"\n{city}:")
        city_data = df[df['city'] == city][['city', 'cityId']].drop_duplicates()
        print(city_data)
else:
    print("All cities have unique cityId values")

# Check if every city has at least one cityId
cities_without_id = df[df['cityId'].isnull()]['city'].unique()

print(f"Cities without cityId: {len(cities_without_id)}")
if len(cities_without_id) > 0:
    print("Cities without cityId:")
    for city in cities_without_id:
        count = df[df['city'] == city].shape[0]
        print(f"  {city}: {count} properties")
else:
    print("All cities have at least one cityId")

# Report missing values before replacement
missing_before = df['cityId'].isnull().sum()
print(f"Missing values in cityId before replacement: {missing_before}")

# Create a mapping of city to cityId for cities with unique cityId
city_to_id = df.dropna(subset=['cityId']).groupby('city')['cityId'].first()

# Fill missing cityId values
missing_mask = df['cityId'].isnull()
for city in df.loc[missing_mask, 'city'].unique():
    if city in city_to_id:
        df.loc[(df['city'] == city) & missing_mask, 'cityId'] = city_to_id[city]

# Report missing values after replacement
missing_after = df['cityId'].isnull().sum()
print(f"Missing values in cityId after replacement: {missing_after}")
print(f"Successfully filled: {missing_before - missing_after} missing values")

# Find columns with exactly 2 distinct values
binary_cols = df.columns[df.nunique() == 2]
print(f"Columns with exactly 2 distinct values: {list(binary_cols)}")

# Convert them to categorical
for col in binary_cols:
    df[col] = df[col].astype('category')
    print(f"Converted {col} to categorical")

print(f"\nConverted {len(binary_cols)} columns to categorical")

# Convert specified columns to categorical
id_cols = ['countyId', 'cityId', 'zipcode']

for col in id_cols:
    if col in df.columns:
        df[col] = df[col].astype('category')
        print(f"Converted {col} to categorical")

print("\nData types after conversion:")
for col in id_cols:
    if col in df.columns:
        print(f"{col}: {df[col].dtype}")

# Check for duplicate rows
initial_rows = len(df)
duplicate_count = df.duplicated().sum()

print(f"Initial number of rows: {initial_rows}")
print(f"Number of duplicate rows: {duplicate_count}")

# Remove duplicates if they exist
if duplicate_count > 0:
    df = df.drop_duplicates(keep='first')
    final_rows = len(df)
    print(f"Rows after removing duplicates: {final_rows}")
    print(f"Removed {initial_rows - final_rows} duplicate rows")
else:
    print("No duplicate rows found")

# Display summary for numerical features
print("=== NUMERICAL FEATURES ===")
print(df.describe())

print("\n=== CATEGORICAL FEATURES ===")
print(df.describe(include='category'))

print("\n=== OBJECT FEATURES ===")
print(df.describe(include='object'))